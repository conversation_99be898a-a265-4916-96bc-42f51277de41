{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"adb": "adb devices", "adb:reconnect": "adb reverse tcp:8081 tcp:8081", "android": "ENVFILE=.env.development &&  react-native run-android --mode=developmentdebug --appId=com.cyclevita", "android:prod": "ENVFILE=.env.production &&  react-native run-android --mode=productionrelease --appId=com.cyclevita", "android:qa": "ENVFILE=.env.uat &&  react-native run-android --mode=qarelease --appId=com.cyclevita", "android:bundle": "cd android && ENVFILE=.env.production && ./gradlew clean && ./gradlew bundleRelease && cd ..", "android:release": "cd android && ENVFILE=.env.production && ./gradlew clean && ./gradlew assembleRelease && cd ..", "clean:npm": "rm -rf node_modules && rm -rf package-lock.json", "clean:ios": "rm -rf ios/Pods && rm -rf ios/Podfile.lock", "clean:yarn": "rm -rf node_modules && rm -rf yarn.lock", "clean:android": "cd android && ./gradlew clean && cd ..", "ios": "npx react-native run-ios", "lint": "eslint .", "pods": "npx pod-install", "packager:reset": "npx react-native start --reset-cache", "start": "npx react-native start", "test": "jest", "type-check": "tsc", "test:report": "jest --collectCoverage --coverageDirectory=\"./coverage\" --ci --reporters=default --reporters=jest-junit --coverage", "pod-install": "cd ios && pod install && cd ..", "postinstall": "npx patch-package", "prepare": "husky"}, "dependencies": {"@gorhom/bottom-sheet": "latest", "@hookform/resolvers": "^3.10.0", "@notifee/react-native": "^9.0.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^21.7.1", "@react-native-firebase/app": "^21.7.1", "@react-native-firebase/messaging": "^21.7.1", "@react-native-firebase/remote-config": "^21.7.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.31.0", "axios": "^1.7.7", "axios-retry": "^4.5.0", "gifted-charts-core": "^0.1.43", "i18next": "^23.11.2", "ky": "^1.2.4", "lottie-react-native": "7.2.2", "react": "18.2.0", "react-hook-form": "7.53.0", "react-i18next": "^14.1.0", "react-native": "0.74.1", "react-native-auth0": "^3.2.1", "react-native-background-secure": "^0.2.0", "react-native-calendars": "^1.1307.0", "react-native-compressor": "^1.10.4", "react-native-config": "^1.5.3", "react-native-device-info": "^14.0.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.21.2", "react-native-gifted-charts": "^1.4.51", "react-native-image-picker": "^7.1.2", "react-native-keyboard-controller": "^1.15.0", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.12.2", "react-native-modal": "^13.0.1", "react-native-pager-view": "^6.4.1", "react-native-reanimated": "3.16.2", "react-native-render-html": "^6.3.4", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "3.31.1", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.7.1", "react-native-toast-message": "^2.2.1", "react-native-video": "^6.10.0", "react-native-vision-camera": "4.6.4", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/eslint-parser": "^7.25.8", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.13.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.1.2", "@types/jest": "^29.4.0", "@types/lodash": "^4.17.14", "@types/node": "^18.14.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.10.0", "@typescript-eslint/parser": "^8.10.0", "babel-jest": "^29.6.3", "babel-plugin-inline-dotenv": "^1.7.0", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-root-import": "^6.6.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.3.1", "eslint": "^9.13.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.1", "eslint-plugin-n": "^17.11.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-promise": "^7.1.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-native": "^4.1.0", "globals": "^15.11.0", "husky": "^9.1.6", "jest": "^29.6.3", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.2.0", "reactotron-react-native": "^5.1.6", "reactotron-react-native-mmkv": "^0.2.6", "reactotron-react-query": "^1.0.4", "typescript": "5.1.3", "typescript-eslint": "^8.10.0"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}