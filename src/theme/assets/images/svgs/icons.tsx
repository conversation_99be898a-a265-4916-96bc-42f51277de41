/** @format */

import { config } from "@/theme/_config";
import * as React from "react";
import Svg, { SvgProps, G, Path, Defs, LinearGradient, Stop, ClipPath, Circle, Rect } from "react-native-svg";

import PillShape1 from "./1.svg";
import PillShape2 from "./2.svg";
import PillShape3 from "./3.svg";
import PillShape4 from "./4.svg";
import PillShape5 from "./5.svg";
import PillShape6 from "./6.svg";
import PillShape7 from "./7.svg";
import PillShape8 from "./8.svg";
import PillShape9 from "./9.svg";
import PillShape10 from "./10.svg";
import TaskClock from "./clock.svg";
import TaskClockDark from "./clock-dark.svg";
import Delete from "./delete.svg";
import FormulaIcon from "./formula-icon.svg";
import FormulaDarkIcon from "./formula-icon-dark.svg";
import SumaryFormulaIcon from "./summary-formula.svg";
import PlayIcon from "./play-icon.svg";
import UncheckedCheckbox from "./checkbox-unchecked-white.svg";
import BellIcon from "./bell.svg";
import AvatarLight from "./avatar-light.svg";
import MedPillIcon from "./Pill.svg";
import MedPillDark from "./Pill-Dark.svg";
import VideoWhiteIcon from "./video-white.svg";
import VideoIcon from "./video.svg";
import FlashOffIcon from "./flash-off-icon.svg";
import CameraFrame from "./camera-frame.svg";
import FlashOnIcon from "./flash-on-icon.svg";
import WideRibbonIcon from "./ribbon-layer.svg";
import PatientIcon from "./patientCharacter.svg";
import HCPIcon from "./hcpCharacter.svg";
import CarerIcon from "./carerCharacter.svg";
import TopBackground from "./disclaimer/top_bg.svg";
import BottomBackground from "./disclaimer/bottom_bg.svg";
import Email from "./email.svg";
import Warning from './warning.svg'
import WarningLight from "./warning-light.svg"
interface CloseIconProps extends SvgProps {
  hasNoCircle?: boolean; // Add `hasCircle` as an optional prop
}

interface InfoIconProps extends SvgProps {
  hasNoCircle?: boolean; // Add `hasCircle` as an optional prop
}

const CheckboxChecked = (props: SvgProps) => {
  return (
    <Svg width={props?.width} height={props?.height} viewBox="0 0 19 19" fill="none" {...props}>
      <Rect x={1.53381} y={1.53381} width={15.338} height={15.338} rx={3.06759} fill={props?.color || "#E5005F"} />
      <Path
        d="M5.752 9.74l2.19 2.147 4.712-5.368"
        stroke="#fff"
        strokeWidth={1.5338}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

const CheckboxUnChecked = (props: SvgProps) => {
  return (
    <Svg width={props?.width} height={props?.height} viewBox="0 0 19 19" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.57 3.834v10.737H3.835V3.834h10.737zm.001-1.533H3.834c-.843 0-1.533.69-1.533 1.534V14.57c0 .844.69 1.534 1.533 1.534h10.737c.844 0 1.534-.69 1.534-1.534V3.835c0-.844-.69-1.534-1.534-1.534z"
        fill={props?.color || "#353535"}
      />
    </Svg>
  );
};

const Logo = (props: SvgProps) => (
  <Svg width={217} height={54} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill={props?.fill || "#fff"}
        d="M137.057 29.461h9.432c.847 0 1.608.178 2.277.534a5.51 5.51 0 0 1 1.705 1.403 6.12 6.12 0 0 1 1.069 2.007c.242.761.366 1.538.366 2.342 0 1.069-.226 2.142-.685 3.21a10.198 10.198 0 0 1-1.84 2.877 9.373 9.373 0 0 1-2.692 2.055 7.122 7.122 0 0 1-3.276.788h-6.588l-1.807 8.531h-3.011l5.05-23.747Zm6.756 12.54a3.94 3.94 0 0 0 1.921-.502 5.359 5.359 0 0 0 1.602-1.338 6.504 6.504 0 0 0 1.085-1.921c.27-.723.399-1.468.399-2.223 0-1.139-.291-2.067-.869-2.795-.582-.723-1.327-1.085-2.239-1.085h-6.221l-2.11 9.87h6.421l.011-.006Zm15.016-12.507h3.011l-2.811 13.111 14.213-13.144h3.275l-11.24 10.468 6.691 13.279h-3.313l-5.617-11.536-4.716 4.279-1.538 7.257h-3.011l5.051-23.714h.005Zm26.823 23.876c-1.403 0-2.585-.205-3.545-.62-.961-.41-1.738-.977-2.342-1.69-.604-.712-1.036-1.548-1.306-2.509-.27-.96-.399-1.996-.399-3.108 0-1.224.156-2.552.469-3.982l2.542-12.005h3.011L181.54 41.46a15.869 15.869 0 0 0-.399 3.48c0 .805.081 1.55.248 ***********.453 1.3.853 1.824.399.523.922.933 1.57 1.22.647.29 1.441.436 2.374.436 1.295 0 2.417-.27 3.378-.804a8.374 8.374 0 0 0 2.476-2.088 11.66 11.66 0 0 0 1.706-2.94 20.207 20.207 0 0 0 1.036-3.362l2.541-12.006h3.011l-2.542 12.006a21.32 21.32 0 0 1-1.489 4.565 14.459 14.459 0 0 1-2.493 3.798 11.225 11.225 0 0 1-3.529 2.59c-1.349.637-2.892.955-4.629.955v-.005Zm23.229-22.781h-1.754l-.955 4.522h-1.171l.955-4.522h-1.753l.253-1.171h4.678l-.253 1.17Zm1.09-1.128h1.656l.664 3.47 2.126-3.47h1.678l-1.209 5.655h-1.171l.88-4.058-2.126 3.238h-.933l-.74-3.238-.858 4.058h-1.171l1.209-5.655h-.005ZM144.649 0h5.164v24.14h-5.164V0Zm18.335 4.565h-7.085V0h19.339v4.565h-7.085V24.14h-5.163V4.565h-.006Zm31.048 15.373h-10.808l-1.56 4.203h-5.644L185.382 0h6.486l9.367 24.14h-5.644l-1.559-4.203Zm-9.492-4.441h8.045l-3.965-11.05-4.085 11.05h.005Z"
      />
      <Path
        fill="url(#b)"
        d="M126.266 24.14h-5.509c-.837-.102-1.619-.48-2.277-1.078-.659-.594-1.198-1.403-1.549-2.37l-3.13-8.622-3.129-8.622c-.653-1.802-1.97-3.065-3.502-3.394a3.38 3.38 0 0 0-.324-.054h6.049c1.673.205 3.129 1.516 3.826 3.448l3.129 8.622 3.13 8.623c.626 1.721 1.856 2.957 3.302 3.345.059.017.048.103-.016.103Z"
      />
      <Path
        fill="url(#c)"
        d="M120.757 24.14h6.043c.836-.102 1.619-.48 2.277-1.078.658-.594 1.198-1.403 1.549-2.37l3.129-8.622 3.13-8.622c.669-1.84 2.023-3.119 3.593-3.41.076-.016.157-.027.232-.038h-6.048c-1.673.205-3.13 1.516-3.826 3.448l-3.13 8.622-3.129 8.623c-.664 1.824-2.007 3.097-3.561 3.405a4.594 4.594 0 0 1-.265.043h.006Z"
      />
      <Path
        fill={props?.fill || "#fff"}
        d="M.905 12.286C.905 4.986 6.177 0 12.765 0c3.345 0 6.162 1.317 8.261 3.74l-.89.782c-1.673-2.1-4.414-3.454-7.371-3.454-5.952 0-10.614 4.522-10.614 11.218s4.662 11.218 10.614 11.218c2.957 0 5.698-1.354 7.37-3.453l.929.75c-2.245 2.53-4.948 3.772-8.299 3.772C6.177 24.567.905 19.587.905 12.286Zm32.159 1.565L23.524.394h1.462l8.655 12.319 8.65-12.32h1.462l-9.545 13.463v10.29h-1.139v-10.29l-.005-.005Zm10.721-1.565C43.785 4.986 49.057 0 55.645 0c3.346 0 6.162 1.317 8.261 3.74l-.89.782c-1.673-2.1-4.414-3.454-7.37-3.454-5.947 0-10.614 4.522-10.614 11.218s4.662 11.218 10.613 11.218c2.957 0 5.698-1.354 7.371-3.453l.928.75c-2.245 2.53-4.948 3.772-8.299 3.772-6.588 0-11.86-4.986-11.86-12.287ZM69.61.394h1.138v22.684h11.93v1.068H69.61V.394Zm17.763 0h14.531v1.068H88.517v9.934h13.138v1.068H88.517v10.614h13.387v1.068H87.373V.394Z"
      />
    </G>
    <Defs>
      <LinearGradient id="b" x1={116.575} x2={116.575} y1={-0.243} y2={23.013} gradientUnits="userSpaceOnUse">
        <Stop stopColor="#13689E" />
        <Stop offset={0.74} stopColor="#E5005F" />
      </LinearGradient>
      <LinearGradient id="c" x1={130.739} x2={130.739} y1={0.027} y2={22.689} gradientUnits="userSpaceOnUse">
        <Stop stopColor="#23B8B1" />
        <Stop offset={0.2} stopColor="#57B885" />
        <Stop offset={0.48} stopColor="#9DB94C" />
        <Stop offset={0.72} stopColor="#CFBA23" />
        <Stop offset={0.9} stopColor="#EFBA09" />
        <Stop offset={1} stopColor="#FBBB00" />
      </LinearGradient>
      <ClipPath id="a">
        <Path fill="#fff" d="M.905 0h215.19v53.37H.905z" />
      </ClipPath>
    </Defs>
  </Svg>
);

const LogoDark = (props: SvgProps) => (
  <Svg width={217} height={54} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#000"
        d="M137.057 29.461h9.432c.847 0 1.608.178 2.277.534a5.51 5.51 0 0 1 1.705 1.403 6.12 6.12 0 0 1 1.069 2.007c.242.761.366 1.538.366 2.342 0 1.069-.226 2.142-.685 3.21a10.198 10.198 0 0 1-1.84 2.877 9.373 9.373 0 0 1-2.692 2.055 7.122 7.122 0 0 1-3.276.788h-6.588l-1.807 8.531h-3.011l5.05-23.747Zm6.756 12.54a3.94 3.94 0 0 0 1.921-.502 5.359 5.359 0 0 0 1.602-1.338 6.504 6.504 0 0 0 1.085-1.921c.27-.723.399-1.468.399-2.223 0-1.139-.291-2.067-.869-2.795-.582-.723-1.327-1.085-2.239-1.085h-6.221l-2.11 9.87h6.421l.011-.006Zm15.016-12.507h3.011l-2.811 13.111 14.213-13.144h3.275l-11.24 10.468 6.691 13.279h-3.313l-5.617-11.536-4.716 4.279-1.538 7.257h-3.011l5.051-23.714h.005Zm26.823 23.876c-1.403 0-2.585-.205-3.545-.62-.961-.41-1.738-.977-2.342-1.69-.604-.712-1.036-1.548-1.306-2.509-.27-.96-.399-1.996-.399-3.108 0-1.224.156-2.552.469-3.982l2.542-12.005h3.011L181.54 41.46a15.869 15.869 0 0 0-.399 3.48c0 .805.081 1.55.248 ***********.453 1.3.853 1.824.399.523.922.933 1.57 1.22.647.29 1.441.436 2.374.436 1.295 0 2.417-.27 3.378-.804a8.374 8.374 0 0 0 2.476-2.088 11.66 11.66 0 0 0 1.706-2.94 20.207 20.207 0 0 0 1.036-3.362l2.541-12.006h3.011l-2.542 12.006a21.32 21.32 0 0 1-1.489 4.565 14.459 14.459 0 0 1-2.493 3.798 11.225 11.225 0 0 1-3.529 2.59c-1.349.637-2.892.955-4.629.955v-.005Zm23.229-22.781h-1.754l-.955 4.522h-1.171l.955-4.522h-1.753l.253-1.171h4.678l-.253 1.17Zm1.09-1.128h1.656l.664 3.47 2.126-3.47h1.678l-1.209 5.655h-1.171l.88-4.058-2.126 3.238h-.933l-.74-3.238-.858 4.058h-1.171l1.209-5.655h-.005ZM144.649 0h5.164v24.14h-5.164V0Zm18.335 4.565h-7.085V0h19.339v4.565h-7.085V24.14h-5.163V4.565h-.006Zm31.048 15.373h-10.808l-1.56 4.203h-5.644L185.382 0h6.486l9.367 24.14h-5.644l-1.559-4.203Zm-9.492-4.441h8.045l-3.965-11.05-4.085 11.05h.005Z"
      />
      <Path
        fill="url(#b)"
        d="M126.266 24.14h-5.509c-.837-.102-1.619-.48-2.277-1.078-.659-.594-1.198-1.403-1.549-2.37l-3.13-8.622-3.129-8.622c-.653-1.802-1.97-3.065-3.502-3.394a3.38 3.38 0 0 0-.324-.054h6.049c1.673.205 3.129 1.516 3.826 3.448l3.129 8.622 3.13 8.623c.626 1.721 1.856 2.957 3.302 3.345.059.017.048.103-.016.103Z"
      />
      <Path
        fill="url(#c)"
        d="M120.757 24.14h6.043c.836-.102 1.619-.48 2.277-1.078.658-.594 1.198-1.403 1.549-2.37l3.129-8.622 3.13-8.622c.669-1.84 2.023-3.119 3.593-3.41.076-.016.157-.027.232-.038h-6.048c-1.673.205-3.13 1.516-3.826 3.448l-3.13 8.622-3.129 8.623c-.664 1.824-2.007 3.097-3.561 3.405a4.594 4.594 0 0 1-.265.043h.006Z"
      />
      <Path
        fill="#000"
        d="M.905 12.286C.905 4.986 6.177 0 12.765 0c3.345 0 6.162 1.317 8.261 3.74l-.89.782c-1.673-2.1-4.414-3.454-7.371-3.454-5.952 0-10.614 4.522-10.614 11.218s4.662 11.218 10.614 11.218c2.957 0 5.698-1.354 7.37-3.453l.929.75c-2.245 2.53-4.948 3.772-8.299 3.772C6.177 24.567.905 19.587.905 12.286Zm32.159 1.565L23.524.394h1.462l8.655 12.319 8.65-12.32h1.462l-9.545 13.463v10.29h-1.139v-10.29l-.005-.005Zm10.721-1.565C43.785 4.986 49.057 0 55.645 0c3.346 0 6.162 1.317 8.261 3.74l-.89.782c-1.673-2.1-4.414-3.454-7.37-3.454-5.947 0-10.614 4.522-10.614 11.218s4.662 11.218 10.613 11.218c2.957 0 5.698-1.354 7.371-3.453l.928.75c-2.245 2.53-4.948 3.772-8.299 3.772-6.588 0-11.86-4.986-11.86-12.287ZM69.61.394h1.138v22.684h11.93v1.068H69.61V.394Zm17.763 0h14.531v1.068H88.517v9.934h13.138v1.068H88.517v10.614h13.387v1.068H87.373V.394Z"
      />
    </G>
    <Defs>
      <LinearGradient id="b" x1={116.575} x2={116.575} y1={-0.243} y2={23.013} gradientUnits="userSpaceOnUse">
        <Stop stopColor="#13689E" />
        <Stop offset={0.74} stopColor="#E5005F" />
      </LinearGradient>
      <LinearGradient id="c" x1={130.739} x2={130.739} y1={0.027} y2={22.689} gradientUnits="userSpaceOnUse">
        <Stop stopColor="#23B8B1" />
        <Stop offset={0.2} stopColor="#57B885" />
        <Stop offset={0.48} stopColor="#9DB94C" />
        <Stop offset={0.72} stopColor="#CFBA23" />
        <Stop offset={0.9} stopColor="#EFBA09" />
        <Stop offset={1} stopColor="#FBBB00" />
      </LinearGradient>
      <ClipPath id="a">
        <Path fill="#fff" d="M.905 0h215.19v53.37H.905z" />
      </ClipPath>
    </Defs>
  </Svg>
);

const Google = (props: SvgProps) => (
  <Svg width={19} height={18} preserveAspectRatio="xMidYMid" viewBox="0 0 256 262" {...props}>
    <Path
      fill="#4285F4"
      d="M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622 38.755 30.023 2.685.268c24.659-22.774 38.875-56.282 38.875-96.027"
    />
    <Path
      fill="#34A853"
      d="M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055-34.523 0-63.824-22.773-74.269-54.25l-1.531.13-40.298 31.187-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1"
    />
    <Path
      fill="#FBBC05"
      d="M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82 0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602l42.356-32.782"
    />
    <Path
      fill="#EB4335"
      d="M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0 79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251"
    />
  </Svg>
);

const Facebook = (props: SvgProps) => (
  <Svg width={18} height={19} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="url(#b)"
        d="M7.515 18.28C3.24 17.515 0 13.825 0 9.37c0-4.95 4.05-9 9-9s9 4.05 9 9c0 4.455-3.24 8.145-7.515 8.91l-.495-.405H8.01l-.495.405Z"
      />
      <Path
        fill="#fff"
        d="m12.51 11.89.405-2.52H10.53V7.615c0-.72.27-1.26 1.35-1.26h1.17V4.06c-.63-.09-1.35-.18-1.98-.18-2.07 0-3.51 1.26-3.51 3.51v1.98H5.31v2.52h2.25v6.345a8.286 8.286 0 0 0 2.97 0V11.89h1.98Z"
      />
    </G>
    <Defs>
      <LinearGradient id="b" x1={9} x2={9} y1={17.744} y2={0.367} gradientUnits="userSpaceOnUse">
        <Stop stopColor="#0062E0" />
        <Stop offset={1} stopColor="#19AFFF" />
      </LinearGradient>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 .37h18v18H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);

const Microsoft = (props: SvgProps) => (
  <Svg width={18} height={18} fill="none" {...props}>
    <Path fill="#F1511B" d="M8.555 8.553H0V-.002h8.555v8.555Z" />
    <Path fill="#80CC28" d="M18 8.553H9.445V-.002H18v8.555Z" />
    <Path fill="#00ADEF" d="M8.554 18.002H0V9.447h8.554v8.555Z" />
    <Path fill="#FBBC09" d="M18 18.002H9.445V9.447H18v8.555Z" />
  </Svg>
);

const Apple = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      fill="#000"
      d="M16.47 19.69c-.922 1.375-1.898 2.717-3.384 2.74-1.487.033-1.964-.877-3.65-.877-1.698 0-2.219.854-3.628.91-1.453.055-2.551-1.465-3.483-2.807-1.897-2.74-3.35-7.788-1.398-11.182.965-1.687 2.696-2.751 4.57-2.785 1.42-.022 2.774.965 3.65.965.866 0 2.508-1.187 4.227-1.01.721.034 2.74.29 4.038 2.197-.1.067-2.407 1.42-2.385 4.227.033 3.35 2.94 4.47 2.973 4.482-.033.078-.466 1.597-1.53 3.14ZM10.134 1.94c.81-.921 2.152-1.62 3.261-1.664.144 1.298-.377 2.607-1.154 3.538-.765.943-2.03 1.676-3.272 1.576-.167-1.276.455-2.607 1.165-3.45Z"
    />
  </Svg>
);

/* Bottom Tab Icons */

const HomeTabIcon = (props: SvgProps) => (
  <Svg width={25} height={26} fill="none" {...props}>
    <Path
      fill={props?.color ?? "#fff"}
      fillRule="evenodd"
      d="M9.744 1.071a4.081 4.081 0 0 1 5.512 0l7.433 6.726a5.568 5.568 0 0 1 1.811 4.13v9.742c0 2.392-1.874 4.331-4.186 4.331H4.686C2.374 26 .5 24.061.5 21.67v-9.744c0-1.582.66-3.087 1.811-4.129l7.433-6.726Zm4.41 1.304a2.449 2.449 0 0 0-3.308 0L3.414 9.1a3.81 3.81 0 0 0-1.24 2.825v9.743c0 1.436 1.125 2.599 2.512 2.599h15.628c1.387 0 2.512-1.163 2.512-2.599v-9.743a3.81 3.81 0 0 0-1.24-2.825l-7.432-6.726Z"
      clipRule="evenodd"
    />
    <Path
      fill={props?.color ?? "#fff"}
      fillRule="evenodd"
      d="M9.286 18.885c0-.5.393-.907.877-.907h4.673c.484 0 .877.406.877.907 0 .5-.393.906-.877.906h-4.673c-.484 0-.877-.405-.877-.906Z"
      clipRule="evenodd"
    />
  </Svg>
);

const TaskTabIcon = (props: SvgProps) => (
  <Svg width={25} height={26} fill="none" {...props}>
    <Path
      fill={props?.color ?? "#fff"}
      d="M.583 3.52A3.52 3.52 0 0 1 4.104 0H18.73a3.52 3.52 0 0 1 3.521 3.52v13.14c-.13.087-.254.189-.37.304l-1.255 1.256V3.52a1.896 1.896 0 0 0-1.896-1.895H4.104a1.896 1.896 0 0 0-1.896 1.896v18.958c0 1.047.85 1.896 1.896 1.896h8.74L14.47 26H4.104a3.52 3.52 0 0 1-3.52-3.521V3.52Z"
    />
    <Path
      fill={props?.color ?? "#fff"}
      d="M7.625 7.312a1.354 1.354 0 1 1-2.708 0 1.354 1.354 0 0 1 2.708 0ZM6.27 14.354a1.354 1.354 0 1 0 0-2.708 1.354 1.354 0 0 0 0 2.708ZM6.27 20.042a1.354 1.354 0 1 0 .001-2.709 1.354 1.354 0 0 0 0 2.709ZM10.063 6.5a.813.813 0 0 0 0 1.625h7.041a.813.813 0 0 0 0-1.625h-7.041ZM9.25 13c0-.449.364-.813.813-.813h7.041a.812.812 0 1 1 0 1.625h-7.041A.813.813 0 0 1 9.25 13ZM10.063 17.875a.813.813 0 0 0 0 1.625h7.041a.813.813 0 0 0 0-1.625h-7.041ZM24.179 19.262l-6.5 6.5a.812.812 0 0 1-1.15 0l-3.247-3.248a.813.813 0 0 1 1.149-1.149l2.673 2.673 5.926-5.925a.812.812 0 1 1 1.149 1.149Z"
    />
  </Svg>
);

const LabTabIcon = (props: SvgProps) => (
  <Svg width={23} height={28} fill="none" {...props}>
    <Path
      stroke={props?.color ?? "#fff"}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M21.5 17v-3.5A4.5 4.5 0 0 0 17 9h-2a1.5 1.5 0 0 1-1.5-1.5v-2A4.5 4.5 0 0 0 9 1H6.5m1 19v1m4-4v4m4-7v7m-6-20H3a1.5 1.5 0 0 0-1.5 1.5v23A1.5 1.5 0 0 0 3 27h17a1.5 1.5 0 0 0 1.5-1.5V13a12 12 0 0 0-12-12Z"
    />
  </Svg>
);

const DietTabIcon = (props: SvgProps) => (
  <Svg width={27} height={26} fill="none" {...props}>
    <Path
      stroke={props?.color ?? "#fff"}
      strokeWidth={1.083}
      d="M6.991 9.48h.542V4.874a.22.22 0 0 1 .017-.056.433.433 0 0 1 .072-.115c.066-.076.13-.1.182-.1.051 0 .116.024.181.1a.433.433 0 0 1 .073.115.22.22 0 0 1 .017.056v5.363c0 1.171-.916 2.094-1.963 2.225l-.475.059v11.853c0 .364-.119.626-.288.795-.17.17-.43.288-.795.288-.365 0-.626-.119-.795-.288-.17-.169-.289-.43-.289-.795V12.522l-.474-.06c-1.047-.13-1.963-1.053-1.963-2.224V4.875a.22.22 0 0 1 .017-.056.433.433 0 0 1 .072-.115c.066-.076.13-.1.182-.1.12 0 .179.038.206.065.027.027.065.085.065.206v4.604h2.708V4.875c0-.004.003-.023.017-.056a.433.433 0 0 1 .072-.115c.066-.076.13-.1.182-.1.12 0 .179.038.206.065.027.027.065.085.065.206v4.604H6.99Zm6.23 5.145v-.542H11.594V4.875c0-2.28 1.601-4.05 3.792-4.303v23.803c0 .364-.119.626-.288.795-.17.17-.43.288-.795.288-.365 0-.626-.119-.796-.288-.169-.169-.288-.43-.288-.795v-9.75Zm5.685-5.968.002-.022v-.022c0-1.357.415-2.581 1.07-3.454.653-.871 1.521-1.367 2.45-1.367.93 0 1.798.496 2.452 1.367.654.873 1.07 2.097 1.07 3.454v.022l.002.022c.144 1.73-.717 3.343-2.209 4.387l-.23.162v11.169c0 .364-.12.626-.29.795-.168.17-.43.288-.794.288-.365 0-.626-.119-.795-.288-.17-.169-.289-.43-.289-.795V13.206l-.23-.162c-1.492-1.044-2.353-2.657-2.21-4.387Z"
    />
  </Svg>
);

const Hamburger = (props: SvgProps) => (
  <Svg width={29} height={22} fill="none" {...props}>
    <Path
      stroke={props?.color || "#fff"}
      strokeLinecap="round"
      strokeWidth={2.4}
      d="M27.6 2H2M27.6 11.23H2M27.6 20.46H2"
    />
  </Svg>
);

const BackArrow = (props: SvgProps) => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M11.01 8.35999L7.37 12M7.37 12L11.01 15.64M7.37 12H16.47M22.84 12C22.84 13.434 22.5575 14.854 22.0088 16.1789C21.46 17.5038 20.6556 18.7076 19.6416 19.7216C18.6276 20.7356 17.4238 21.54 16.0989 22.0888C14.774 22.6375 13.354 22.92 11.92 22.92C10.486 22.92 9.06597 22.6375 7.7411 22.0888C6.41622 21.54 5.21241 20.7356 4.19839 19.7216C3.18438 18.7076 2.38002 17.5038 1.83124 16.1789C1.28245 14.854 1 13.434 1 12C1 9.10382 2.1505 6.32628 4.19839 4.27838C6.24629 2.23048 9.02384 1.07999 11.92 1.07999C14.8162 1.07999 17.5937 2.23048 19.6416 4.27838C21.6895 6.32628 22.84 9.10382 22.84 12Z"
      stroke={props?.color || "#fff"}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Chevron: React.FC<SvgProps> = ({ width = 13, height = 8, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 13 8" fill="none" {...props}>
    <Path
      d="M6.4519 7.34961C6.30802 7.34961 6.17521 7.32194 6.05347 7.2666C5.93172 7.2168 5.81274 7.13379 5.69653 7.01758L0.77417 2.03711C0.591553 1.86003 0.500244 1.63867 0.500244 1.37305C0.500244 1.10189 0.591553 0.872232 0.77417 0.684081C0.962321 0.501464 1.18921 0.410156 1.45483 0.410156C1.72046 0.410156 1.95565 0.509765 2.1604 0.708984L6.4519 5.06689L10.7434 0.708984C10.9482 0.509765 11.1833 0.410156 11.449 0.410156C11.7146 0.410156 11.9387 0.501465 12.1213 0.684082C12.3095 0.872233 12.4036 1.10189 12.4036 1.37305C12.4036 1.63867 12.3123 1.86003 12.1296 2.03711L7.20727 7.01758C6.99145 7.23893 6.73966 7.34961 6.4519 7.34961Z"
      fill={props?.color ?? "#9C9B9B"}
    />
  </Svg>
);

const Profile: React.FC<SvgProps> = ({ color = config.colors.yellowBlue, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 15 15" fill="none" {...props}>
    <Path
      d="M11.8209 12.3569C11.3162 11.6888 10.6633 11.1469 9.91358 10.774C9.16385 10.4012 8.33779 10.2075 7.50047 10.2083C6.66314 10.2075 5.83708 10.4012 5.08735 10.774C4.33762 11.1469 3.68472 11.6888 3.18006 12.3569M11.8209 12.3569C12.8056 11.481 13.5001 10.3264 13.8136 9.04626C14.1271 7.76612 14.0441 6.42094 13.5755 5.18909C13.1069 3.95724 12.275 2.89693 11.1899 2.14879C10.1049 1.40064 8.81806 1 7.5001 1C6.18214 1 4.89532 1.40064 3.81029 2.14879C2.72526 2.89693 1.89328 3.95724 1.4247 5.18909C0.956118 6.42094 0.87307 7.76612 1.18657 9.04626C1.50007 10.3264 2.1953 11.481 3.18006 12.3569M11.8209 12.3569C10.6321 13.4173 9.09344 14.0023 7.50047 14C5.90724 14.0024 4.36903 13.4174 3.18006 12.3569M9.66717 5.87486C9.66717 6.44951 9.43889 7.00062 9.03255 7.40695C8.62622 7.81329 8.07511 8.04157 7.50047 8.04157C6.92582 8.04157 6.37471 7.81329 5.96838 7.40695C5.56204 7.00062 5.33376 6.44951 5.33376 5.87486C5.33376 5.30022 5.56204 4.74911 5.96838 4.34277C6.37471 3.93644 6.92582 3.70816 7.50047 3.70816C8.07511 3.70816 8.62622 3.93644 9.03255 4.34277C9.43889 4.74911 9.66717 5.30022 9.66717 5.87486Z"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ForwardArrow = (props: SvgProps) => (
  <Svg width="14" height="11" viewBox="0 0 14 11" fill="none">
    <Path
      d="M1.25709 5.65725L12.9176 6.00409M12.9176 6.00409L8.41486 1.50132M12.9176 6.00409L8.67499 10.2467"
      stroke={props?.color ?? "#9C9B9B"}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Mail = (props: SvgProps) => (
  <Svg width={props.width} height={props.height} viewBox="0 0 14 11" fill="none">
    <Path
      d="M0.0342894 1.49116C0.0924768 1.19514 0.223672 0.915371 0.415078 0.680163C0.453961 0.630781 0.49462 0.58685 0.539082 0.542286C0.879621 0.197663 1.35332 0 1.83876 0H12.1613C12.6521 0 13.1138 0.192681 13.4612 0.542479C13.5052 0.58663 13.5459 0.631001 13.5863 0.681952C13.7769 0.916169 13.9075 1.19556 13.9643 1.49198C13.988 1.60839 14 1.72901 14 1.85103V9.14914C14 9.40304 13.9488 9.65027 13.8472 9.88531C13.7593 10.094 13.6259 10.2919 13.4613 10.4575C13.4198 10.4991 13.3787 10.5374 13.3347 10.5747C13.0056 10.849 12.589 11 12.1613 11H1.83876C1.40831 11 0.992058 10.8485 0.663878 10.5717C0.620019 10.536 0.579359 10.4985 0.538699 10.4575C0.379613 10.2975 0.253176 10.1137 0.162285 9.91102L0.150309 9.88212C0.0506134 9.65154 0 9.4051 0 9.14917V1.85103C0 1.7306 0.0115395 1.60919 0.0342894 1.49116ZM13.0478 1.1813C13.0218 1.14298 12.9886 1.10365 12.9463 1.06068C12.736 0.849144 12.4572 0.732737 12.1613 0.732737H1.83879C1.54033 0.732737 1.26142 0.849336 1.0535 1.06109C1.0176 1.09762 0.983309 1.13816 0.953395 1.17868L0.874454 1.28548L6.38422 6.11945C6.55408 6.26936 6.77277 6.35185 7.00008 6.35185C7.22515 6.35185 7.44365 6.26955 7.61554 6.11965L13.1198 1.28686L13.0478 1.1813ZM13.2684 9.21358C13.2714 9.1937 13.2721 9.17163 13.2721 9.14914V2.00214L8.99634 5.75697L13.2289 9.47188L13.2684 9.21358ZM1.28792 10.1199C1.4534 10.216 1.64399 10.267 1.83876 10.267H12.1613C12.3562 10.267 12.5467 10.216 12.7121 10.1199L12.8851 10.019L8.51184 6.18243L8.03255 6.60209C7.74506 6.85335 7.37844 6.99186 7.00003 6.99186C6.62022 6.99186 6.253 6.85335 5.9657 6.60209L5.48661 6.18224L1.11486 10.0192L1.28792 10.1199ZM0.728109 9.14914C0.728109 9.17124 0.728519 9.1931 0.731281 9.21256L0.76918 9.47328L5.00358 5.75898L0.728109 2.00396V9.14914Z"
      fill={props?.color ?? config.colors.yellowBlue}
    />
  </Svg>
);

const Support = (props: SvgProps) => (
  <Svg width="21" height="18" viewBox="0 0 14 11" fill="none">
    <Path
      d="M0.0342894 1.49116C0.0924768 1.19514 0.223672 0.915371 0.415078 0.680163C0.453961 0.630781 0.49462 0.58685 0.539082 0.542286C0.879621 0.197663 1.35332 0 1.83876 0H12.1613C12.6521 0 13.1138 0.192681 13.4612 0.542479C13.5052 0.58663 13.5459 0.631001 13.5863 0.681952C13.7769 0.916169 13.9075 1.19556 13.9643 1.49198C13.988 1.60839 14 1.72901 14 1.85103V9.14914C14 9.40304 13.9488 9.65027 13.8472 9.88531C13.7593 10.094 13.6259 10.2919 13.4613 10.4575C13.4198 10.4991 13.3787 10.5374 13.3347 10.5747C13.0056 10.849 12.589 11 12.1613 11H1.83876C1.40831 11 0.992058 10.8485 0.663878 10.5717C0.620019 10.536 0.579359 10.4985 0.538699 10.4575C0.379613 10.2975 0.253176 10.1137 0.162285 9.91102L0.150309 9.88212C0.0506134 9.65154 0 9.4051 0 9.14917V1.85103C0 1.7306 0.0115395 1.60919 0.0342894 1.49116ZM13.0478 1.1813C13.0218 1.14298 12.9886 1.10365 12.9463 1.06068C12.736 0.849144 12.4572 0.732737 12.1613 0.732737H1.83879C1.54033 0.732737 1.26142 0.849336 1.0535 1.06109C1.0176 1.09762 0.983309 1.13816 0.953395 1.17868L0.874454 1.28548L6.38422 6.11945C6.55408 6.26936 6.77277 6.35185 7.00008 6.35185C7.22515 6.35185 7.44365 6.26955 7.61554 6.11965L13.1198 1.28686L13.0478 1.1813ZM13.2684 9.21358C13.2714 9.1937 13.2721 9.17163 13.2721 9.14914V2.00214L8.99634 5.75697L13.2289 9.47188L13.2684 9.21358ZM1.28792 10.1199C1.4534 10.216 1.64399 10.267 1.83876 10.267H12.1613C12.3562 10.267 12.5467 10.216 12.7121 10.1199L12.8851 10.019L8.51184 6.18243L8.03255 6.60209C7.74506 6.85335 7.37844 6.99186 7.00003 6.99186C6.62022 6.99186 6.253 6.85335 5.9657 6.60209L5.48661 6.18224L1.11486 10.0192L1.28792 10.1199ZM0.728109 9.14914C0.728109 9.17124 0.728519 9.1931 0.731281 9.21256L0.76918 9.47328L5.00358 5.75898L0.728109 2.00396V9.14914Z"
      fill={props?.color ?? config.colors.white}
    />
  </Svg>
);

const Phone = (props: SvgProps) => (
  <Svg width={props.width} height={props.height} viewBox="0 0 14 14" fill="none">
    <Path
      d="M1 3.76923C1 8.86708 5.13292 13 10.2308 13H11.6154C11.9826 13 12.3348 12.8541 12.5945 12.5945C12.8541 12.3348 13 11.9826 13 11.6154V10.7711C13 10.4535 12.784 10.1766 12.4757 10.0997L9.75385 9.41908C9.48308 9.35138 9.19877 9.45292 9.032 9.67569L8.43508 10.4714C8.26154 10.7028 7.96185 10.8049 7.69046 10.7052C6.683 10.3348 5.76809 9.7499 5.0091 8.9909C4.2501 8.23191 3.66515 7.317 3.29477 6.30954C3.19508 6.03815 3.29723 5.73846 3.52862 5.56492L4.32431 4.968C4.54769 4.80123 4.64862 4.51631 4.58092 4.24615L3.90031 1.52431C3.86285 1.37458 3.77644 1.24166 3.6548 1.14667C3.53316 1.05167 3.38326 1.00005 3.22892 1H2.38462C2.01739 1 1.66521 1.14588 1.40554 1.40554C1.14588 1.66521 1 2.01739 1 2.38462V3.76923Z"
      stroke={props?.color ?? config.colors.yellowBlue}
      stroke-width="0.923077"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Pencil: React.FC<SvgProps> = ({ color, width = 10, height = 10, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 12 12" fill="none">
    <Path
      d="M8.09321 2.14729L8.95833 1.28166C9.13868 1.10132 9.38328 1 9.63832 1C9.89337 1 10.138 1.10132 10.3183 1.28166C10.4987 1.46201 10.6 1.70661 10.6 1.96165C10.6 2.2167 10.4987 2.4613 10.3183 2.64164L2.94969 10.0103C2.67858 10.2812 2.34424 10.4804 1.97688 10.5897L0.599976 11L1.01023 9.62309C1.1196 9.25573 1.31875 8.9214 1.58971 8.65029L8.09373 2.14729H8.09321ZM8.09321 2.14729L9.44602 3.50009"
      stroke={color || "white"}
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Camera: React.FC<SvgProps> = ({ color = "white", width = 10, height = 10, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 15 13" fill="none" {...props}>
    <Path
      d="M3.98195 2.58004C3.86467 2.76566 3.7082 2.92337 3.52351 3.04211C3.33882 3.16085 3.13038 3.23773 2.91282 3.26738C2.66525 3.30256 2.41963 3.34035 2.17402 3.38139C1.48798 3.49541 1 4.09935 1 4.79451V10.2841C1 10.6729 1.15444 11.0457 1.42935 11.3206C1.70426 11.5956 2.07711 11.75 2.46589 11.75H12.2385C12.6273 11.75 13.0001 11.5956 13.275 11.3206C13.5499 11.0457 13.7044 10.6729 13.7044 10.2841V4.79451C13.7044 4.09935 13.2158 3.49541 12.5304 3.38139C12.2846 3.34044 12.0383 3.30243 11.7916 3.26738C11.5741 3.23764 11.3658 3.16071 11.1812 3.04198C10.9967 2.92325 10.8403 2.76558 10.7231 2.58004L10.1876 1.72266C10.0673 1.52727 9.90169 1.36374 9.70481 1.24591C9.50794 1.12808 9.28557 1.05942 9.05654 1.04574C7.92113 0.984754 6.78326 0.984754 5.64785 1.04574C5.41882 1.05942 5.19645 1.12808 4.99958 1.24591C4.8027 1.36374 4.63711 1.52727 4.51683 1.72266L3.98195 2.58004Z"
      stroke={color}
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <Path
      d="M10.2845 6.86342C10.2845 7.64098 9.97558 8.38669 9.42576 8.9365C8.87595 9.48632 8.13024 9.7952 7.35268 9.7952C6.57512 9.7952 5.82941 9.48632 5.2796 8.9365C4.72978 8.38669 4.4209 7.64098 4.4209 6.86342C4.4209 6.08587 4.72978 5.34016 5.2796 4.79034C5.82941 4.24052 6.57512 3.93164 7.35268 3.93164C8.13024 3.93164 8.87595 4.24052 9.42576 4.79034C9.97558 5.34016 10.2845 6.08587 10.2845 6.86342ZM11.7504 5.39753H11.7556V5.40274H11.7504V5.39753Z"
      stroke={color}
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Close: React.FC<CloseIconProps> = ({
  hasNoCircle = false, // Default to `false`
  color = "white",
  width = 24,
  height = 24,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 32 32" fill="none" {...props}>
    {/* Circle Outline */}
    {!hasNoCircle && <Circle cx="16" cy="16" r="15" stroke={color} strokeWidth="2" fill="none" />}

    {/* X Mark */}
    <Path d="M20 12 L12 20 M12 12 L20 20" stroke={color} strokeWidth="2" strokeLinecap="round" />
  </Svg>
);

const CloseWhite: React.FC<CloseIconProps> = ({
  hasNoCircle = false, // Default to `false`
  color = "white",
  width = 12,
  height = 12,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 12 12" fill="none">
    <Path d="M1 11L11 1M1 1L11 11" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
  </Svg>
);

const Info: React.FC<InfoIconProps> = ({
  hasNoCircle = false, // Default to `false`
  color = "white",
  width = 24,
  height = 24,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 14 14" fill="none" {...props}>
    {/* Circle Outline */}
    {!hasNoCircle && (
      <Path
        d="M6.5 6.51514L6.52733 6.5018C6.61282 6.4591 6.70875 6.44178 6.80378 6.45191C6.8988 6.46203 6.98893 6.49918 7.0635 6.55894C7.13806 6.61871 7.19394 6.69859 7.22451 6.78913C7.25508 6.87967 7.25907 6.97707 7.236 7.0698L6.764 8.96047C6.74076 9.05325 6.74463 9.15075 6.77513 9.2414C6.80563 9.33205 6.86149 9.41205 6.93609 9.47191C7.01069 9.53177 7.10089 9.56898 7.196 9.57912C7.2911 9.58927 7.38712 9.57192 7.47267 9.52914L7.5 9.51514M13 7.01514C13 7.80307 12.8448 8.58328 12.5433 9.31124C12.2417 10.0392 11.7998 10.7006 11.2426 11.2578C10.6855 11.8149 10.0241 12.2569 9.2961 12.5584C8.56815 12.8599 7.78793 13.0151 7 13.0151C6.21207 13.0151 5.43185 12.8599 4.7039 12.5584C3.97595 12.2569 3.31451 11.8149 2.75736 11.2578C2.20021 10.7006 1.75825 10.0392 1.45672 9.31124C1.15519 8.58328 1 7.80307 1 7.01514C1 5.42384 1.63214 3.89771 2.75736 2.7725C3.88258 1.64728 5.4087 1.01514 7 1.01514C8.5913 1.01514 10.1174 1.64728 11.2426 2.7725C12.3679 3.89771 13 5.42384 13 7.01514ZM7 4.51514H7.00533V4.52047H7V4.51514Z"
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    )}

    {/* If hasNoCircle is true, render just the info icon without the circle */}
    {hasNoCircle && (
      <Path
        d="M6.5 6.51514L6.52733 6.5018C6.61282 6.4591 6.70875 6.44178 6.80378 6.45191C6.8988 6.46203 6.98893 6.49918 7.0635 6.55894C7.13806 6.61871 7.19394 6.69859 7.22451 6.78913C7.25508 6.87967 7.25907 6.97707 7.236 7.0698L6.764 8.96047C6.74076 9.05325 6.74463 9.15075 6.77513 9.2414C6.80563 9.33205 6.86149 9.41205 6.93609 9.47191C7.01069 9.53177 7.10089 9.56898 7.196 9.57912C7.2911 9.58927 7.38712 9.57192 7.47267 9.52914L7.5 9.51514M7 4.51514H7.00533V4.52047H7V4.51514Z"
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    )}
  </Svg>
);

const Contacts: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 22 22" fill="none" {...props}>
    <Path
      d="M16.1878 14.6475C16.6676 14.2771 17.2303 14.0679 17.8109 14.044C18.3916 14.0201 18.9663 14.1824 19.4687 14.5124C19.9711 14.8423 20.3805 15.3262 20.6497 15.9082C20.919 16.4903 21.0369 17.1465 20.9899 17.8011C19.7593 18.2873 18.4522 18.4765 17.1529 18.3565C17.1489 17.0424 16.8142 15.7557 16.1878 14.6487C15.6318 13.6629 14.8648 12.8516 13.9578 12.2899C13.0508 11.7281 12.0332 11.434 10.9989 11.4348C9.96491 11.4342 8.94744 11.7284 8.04066 12.2901C7.13387 12.8519 6.36705 13.6631 5.81114 14.6487M17.1519 18.3553L17.1529 18.3913C17.1529 18.6522 17.1406 18.9096 17.115 19.1635C15.2538 20.3706 13.1447 21.0039 10.9989 21C8.77326 21 6.68398 20.3322 4.88292 19.1635C4.85655 18.8951 4.84388 18.6253 4.84497 18.3553M4.84497 18.3553C3.5461 18.4797 2.23969 18.2912 1.01001 17.8023C0.963194 17.1479 1.08119 16.4918 1.35036 15.91C1.61953 15.3282 2.02888 14.8444 2.53107 14.5145C3.03327 14.1847 3.60781 14.0222 4.18826 14.0459C4.7687 14.0697 5.33135 14.2786 5.81114 14.6487M4.84497 18.3553C4.84865 17.0414 5.1851 15.7558 5.81114 14.6487M14.0759 4.47826C14.0759 5.40075 13.7518 6.28546 13.1747 6.93776C12.5977 7.59006 11.815 7.95652 10.9989 7.95652C10.1829 7.95652 9.40023 7.59006 8.82319 6.93776C8.24614 6.28546 7.92196 5.40075 7.92196 4.47826C7.92196 3.55577 8.24614 2.67106 8.82319 2.01876C9.40023 1.36646 10.1829 1 10.9989 1C11.815 1 12.5977 1.36646 13.1747 2.01876C13.7518 2.67106 14.0759 3.55577 14.0759 4.47826ZM20.2299 7.95652C20.2299 8.29909 20.1702 8.63832 20.0542 8.95482C19.9383 9.27132 19.7683 9.5589 19.554 9.80114C19.3397 10.0434 19.0853 10.2355 18.8053 10.3666C18.5253 10.4977 18.2252 10.5652 17.9222 10.5652C17.6191 10.5652 17.319 10.4977 17.039 10.3666C16.759 10.2355 16.5046 10.0434 16.2903 9.80114C16.0761 9.5589 15.9061 9.27132 15.7901 8.95482C15.6741 8.63832 15.6144 8.29909 15.6144 7.95652C15.6144 7.26465 15.8576 6.60112 16.2903 6.11189C16.7231 5.62267 17.3101 5.34782 17.9222 5.34782C18.5342 5.34782 19.1212 5.62267 19.554 6.11189C19.9868 6.60112 20.2299 7.26465 20.2299 7.95652ZM6.38346 7.95652C6.38346 8.29909 6.32377 8.63832 6.2078 8.95482C6.09182 9.27132 5.92183 9.5589 5.70754 9.80114C5.49325 10.0434 5.23884 10.2355 4.95886 10.3666C4.67887 10.4977 4.37878 10.5652 4.07572 10.5652C3.77266 10.5652 3.47257 10.4977 3.19259 10.3666C2.9126 10.2355 2.6582 10.0434 2.4439 9.80114C2.22961 9.5589 2.05962 9.27132 1.94365 8.95482C1.82767 8.63832 1.76798 8.29909 1.76798 7.95652C1.76798 7.26465 2.01112 6.60112 2.4439 6.11189C2.87669 5.62267 3.46367 5.34782 4.07572 5.34782C4.68777 5.34782 5.27476 5.62267 5.70754 6.11189C6.14033 6.60112 6.38346 7.26465 6.38346 7.95652Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Resources: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 20 19" fill="none" {...props}>
    <Path
      d="M10 18.3333L0 13.3333L4.08333 11.25L0 9.16667L4.08333 7.08333L0 5L10 0L20 5L15.9167 7.08333L20 9.16667L15.9167 11.25L20 13.3333L10 18.3333ZM3.66667 13.3333L10.0833 16.5833L16.4167 13.3333L14.0833 12.1667L10 14.1667L5.91667 12.0833L3.66667 13.3333ZM3.66667 9.16667L10.0833 12.4167L16.4167 9.16667L14.0833 8L10 10.0833L5.91667 8L3.66667 9.16667ZM3.66667 5L10.0833 8.25L16.4167 5L10 1.83333L3.66667 5Z"
      fill={color || "white"}
    />
  </Svg>
);

const CheckBoard: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 15 19" fill="none" {...props}>
    <Path
      d="M15 18.1666H0V1.66663H3.75V0.166626H11.25V1.66663H15V18.1666ZM1.5 16.6666H13.5V3.16663H11.25V5.41663H3.75V3.16663H1.5V16.6666ZM5.25 3.91663H9.75V1.66663H5.25V3.91663ZM6.75 13.9666L3.975 11.1916L5.025 10.1416L6.75 11.8666L10.725 7.89163L11.775 8.94163L6.75 13.9666Z"
      fill={color || "white"}
    />
  </Svg>
);

const MedicationPill = (props: SvgProps) => (
  <Svg width="25" height="25" viewBox="0 0 25 25" fill="none">
    <Path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M14.2189 4.6838L12.2117 6.68739L9.74602 9.15849C9.73335 9.96272 9.88241 10.7613 10.1843 11.5068C10.4863 12.2523 10.9349 12.9295 11.5037 13.4983C12.0724 14.067 12.7497 14.5157 13.4952 14.8176C14.2407 15.1195 15.0392 15.2686 15.8434 15.2559L18.3127 12.7884L20.3163 10.7848C21.0937 9.96983 21.5214 8.88315 21.5083 7.75697C21.4951 6.63079 21.0421 5.55441 20.2459 4.75779C19.4498 3.96117 18.3737 3.50749 17.2475 3.49366C16.1213 3.47983 15.0344 3.90696 14.2189 4.6838ZM10.7496 4.32279L7.13955 7.93287L2.87965 12.1928C2.20442 12.8394 1.66511 13.6143 1.29335 14.4721C0.921602 15.3299 0.724893 16.2533 0.714767 17.1881C0.704641 18.123 0.881302 19.0504 1.23439 19.9161C1.58747 20.7817 2.10987 21.5681 2.77094 22.2292C3.432 22.8903 4.21843 23.4127 5.08408 23.7658C5.94973 24.1188 6.87718 24.2955 7.81202 24.2854C8.74685 24.2752 9.67025 24.0785 10.5281 23.7068C11.3859 23.335 12.1608 22.7957 12.8074 22.1205L17.0673 17.8606L20.6773 14.2505L22.2297 12.6982C22.8815 12.0463 23.3986 11.2724 23.7514 10.4207C24.1042 9.56904 24.2858 8.65619 24.2858 7.73432C24.2858 6.81244 24.1042 5.89959 23.7514 5.04789C23.3986 4.19619 22.8815 3.42232 22.2297 2.77045C21.5778 2.11859 20.8039 1.6015 19.9522 1.24872C19.1005 0.895932 18.1877 0.714355 17.2658 0.714355C16.3439 0.714355 15.4311 0.895932 14.5794 1.24872C13.7277 1.6015 12.9538 2.11859 12.302 2.77045L10.7496 4.32279ZM10.8904 20.2071L13.4409 17.6584C11.9838 17.2555 10.6558 16.4822 9.58632 15.4136C8.51682 14.3451 7.74228 13.0178 7.3381 11.561L4.79299 14.1097C4.02945 14.9273 3.61334 16.0094 3.63243 17.1279C3.65153 18.2464 4.10435 19.3138 4.89536 20.1048C5.68637 20.8958 6.75371 21.3486 7.8722 21.3677C8.99069 21.3868 10.0729 20.9707 10.8904 20.2071ZM12.5005 8.78124C12.3675 8.90518 12.2608 9.05464 12.1868 9.2207C12.1128 9.38676 12.0731 9.56603 12.0698 9.7478C12.0666 9.92957 12.1001 10.1101 12.1682 10.2787C12.2363 10.4473 12.3376 10.6004 12.4662 10.7289C12.5947 10.8575 12.7478 10.9588 12.9164 11.0269C13.085 11.095 13.2655 11.1285 13.4473 11.1252C13.6291 11.122 13.8083 11.0823 13.9744 11.0083C14.1405 10.9343 14.2899 10.8276 14.4139 10.6946L17.1214 7.98702C17.2544 7.86308 17.3611 7.71363 17.4351 7.54756C17.5091 7.3815 17.5489 7.20224 17.5521 7.02046C17.5553 6.83869 17.5219 6.65813 17.4538 6.48956C17.3857 6.32099 17.2843 6.16787 17.1558 6.03931C17.0272 5.91076 16.8741 5.80942 16.7055 5.74133C16.537 5.67324 16.3564 5.6398 16.1746 5.64301C15.9929 5.64622 15.8136 5.686 15.6475 5.76C15.4815 5.83399 15.332 5.94067 15.2081 6.07368L12.5005 8.78124Z"
      fill={props?.fill || "white"}
    />
  </Svg>
);

const Calendar = (props: SvgProps) => (
  <Svg width="10" height="10" viewBox="0 0 10 10" fill="none" {...props}>
    <Path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.30769 0C2.4097 0 2.50753 0.0405218 2.57966 0.112651C2.65179 0.184781 2.69231 0.282609 2.69231 0.384615V1.15385H7.30769V0.384615C7.30769 0.282609 7.34821 0.184781 7.42034 0.112651C7.49247 0.0405218 7.5903 0 7.69231 0C7.79431 0 7.89214 0.0405218 7.96427 0.112651C8.0364 0.184781 8.07692 0.282609 8.07692 0.384615V1.15385H8.46154C8.86956 1.15385 9.26088 1.31593 9.5494 1.60445C9.83791 1.89297 10 2.28428 10 2.69231V8.46154C10 8.86956 9.83791 9.26088 9.5494 9.5494C9.26088 9.83791 8.86956 10 8.46154 10H1.53846C1.13044 10 0.739123 9.83791 0.450605 9.5494C0.162088 9.26088 0 8.86956 0 8.46154V2.69231C0 2.28428 0.162088 1.89297 0.450605 1.60445C0.739123 1.31593 1.13044 1.15385 1.53846 1.15385H1.92308V0.384615C1.92308 0.282609 1.9636 0.184781 2.03573 0.112651C2.10786 0.0405218 2.20569 0 2.30769 0ZM9.23077 4.61539C9.23077 4.41137 9.14973 4.21572 9.00547 4.07146C8.86121 3.9272 8.66555 3.84615 8.46154 3.84615H1.53846C1.33445 3.84615 1.13879 3.9272 0.994533 4.07146C0.850274 4.21572 0.769231 4.41137 0.769231 4.61539V8.46154C0.769231 8.66555 0.850274 8.86121 0.994533 9.00547C1.13879 9.14973 1.33445 9.23077 1.53846 9.23077H8.46154C8.66555 9.23077 8.86121 9.14973 9.00547 9.00547C9.14973 8.86121 9.23077 8.66555 9.23077 8.46154V4.61539Z"
      fill={props?.fill || "white"}
    />
  </Svg>
);

const FormulaBottle: React.FC<SvgProps> = ({ width = 18, height = 26, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 17 25" fill="none" {...props}>
    <Path
      d="M13.6243 8.06338V6.8633H14.6491C15.2151 6.8633 15.674 6.40444 15.674 5.83845V1.73909C15.674 1.17309 15.2152 0.714233 14.6491 0.714233H2.35097C1.78498 0.714233 1.32612 1.17309 1.32612 1.73909V5.83845C1.32612 6.40444 1.78498 6.8633 2.35097 6.8633H3.37583V8.06338C1.59858 8.58793 0.30127 10.232 0.30127 12.1792V19.9946C0.30127 22.3644 2.22258 24.2857 4.59229 24.2857H12.4078C14.7775 24.2857 16.6988 22.3644 16.6988 19.9946V12.1792C16.6988 10.232 15.4015 8.58793 13.6243 8.06338ZM3.37583 2.76394H13.6243V4.81364H12.5995H4.40068H3.37583V2.76394ZM11.5746 6.8633V7.88816H5.42549V6.8633H11.5746ZM14.6491 19.9947C14.6491 21.2324 13.6455 22.236 12.4078 22.236H4.59229C3.35456 22.236 2.35093 21.2324 2.35093 19.9947V12.1792C2.35093 10.9414 3.35456 9.93782 4.59229 9.93782H12.4078C13.6455 9.93782 14.6491 10.9414 14.6491 12.1792V19.9947Z"
      fill={props?.fill || "white"}
    />
    <Path
      d="M10.0764 16.0879C10.0751 15.9859 10.0545 15.8885 10.0179 15.7994C10.0177 15.799 10.0176 15.7986 10.0175 15.7982C10.0084 15.7761 9.99824 15.7545 9.98726 15.7335C9.98623 15.7315 9.98527 15.7295 9.98421 15.7275C9.97373 15.7079 9.9624 15.6889 9.95036 15.6703C9.94823 15.667 9.94618 15.6636 9.94398 15.6603C9.93219 15.6427 9.91962 15.6257 9.90648 15.6091C9.90357 15.6054 9.90074 15.6016 9.89777 15.598C9.88399 15.5812 9.86944 15.5651 9.85435 15.5496C9.85166 15.5468 9.84907 15.5439 9.84631 15.5412C9.82903 15.5238 9.8109 15.5074 9.79209 15.4917C9.79131 15.4911 9.7906 15.4903 9.78982 15.4897C9.76932 15.4728 9.74786 15.4569 9.72569 15.4421L9.72565 15.4421L9.72555 15.442C9.70356 15.4273 9.68072 15.4137 9.65724 15.4013C9.65639 15.4008 9.65554 15.4004 9.65469 15.4C9.63262 15.3884 9.60996 15.3778 9.58673 15.3683C9.58485 15.3675 9.58297 15.3668 9.5811 15.366C9.55928 15.3573 9.53704 15.3495 9.51434 15.3427C9.51147 15.3419 9.50857 15.3411 9.5057 15.3403C9.4842 15.3341 9.46242 15.3287 9.44022 15.3244C9.4365 15.3237 9.43278 15.3231 9.42906 15.3224C9.40782 15.3186 9.38632 15.3155 9.3645 15.3134C9.3604 15.313 9.35629 15.3128 9.35218 15.3125C9.33111 15.3108 9.30986 15.3097 9.28836 15.3097H9.28582C9.28486 15.3097 9.28394 15.3097 9.28298 15.3097H8.39636L9.15595 14.1703C9.39748 13.808 9.29956 13.3186 8.93731 13.0771C8.57506 12.8355 8.08557 12.9335 7.84408 13.2957L6.27096 15.6554C6.26983 15.6571 6.2687 15.6588 6.2676 15.6605L6.26742 15.6607L6.26732 15.6609C6.25262 15.6829 6.23906 15.7057 6.22663 15.7291C6.22617 15.73 6.22578 15.7308 6.22535 15.7317C6.21374 15.7537 6.20315 15.7763 6.19366 15.7996C6.19288 15.8015 6.19217 15.8034 6.19139 15.8053C6.18268 15.8271 6.17485 15.8493 6.16809 15.872C6.16724 15.8749 6.16646 15.8778 6.16564 15.8806C6.15948 15.9021 6.15417 15.9239 6.14981 15.9461C6.14907 15.9498 6.14847 15.9536 6.14779 15.9573C6.14397 15.9785 6.14085 16 6.13876 16.0218C6.13837 16.0259 6.13816 16.0301 6.13781 16.0342C6.13611 16.0553 6.13501 16.0765 6.13501 16.098C6.13501 16.0985 6.13505 16.099 6.13505 16.0995C6.13505 16.1024 6.13522 16.1053 6.13526 16.1081C6.13657 16.2101 6.15718 16.3074 6.19369 16.3966C6.19387 16.397 6.19401 16.3974 6.19419 16.3979C6.20329 16.42 6.21338 16.4415 6.22439 16.4625C6.22542 16.4645 6.22641 16.4665 6.22744 16.4685C6.23792 16.4881 6.24926 16.5072 6.2613 16.5258C6.26342 16.5291 6.26547 16.5324 6.26767 16.5357C6.27946 16.5533 6.29204 16.5704 6.30517 16.5869C6.30811 16.5907 6.31091 16.5944 6.31392 16.5981C6.32766 16.6148 6.34222 16.6308 6.3573 16.6464C6.36003 16.6492 6.36261 16.6521 6.36538 16.6549C6.38262 16.6722 6.40072 16.6886 6.41949 16.7043C6.4203 16.7049 6.42108 16.7057 6.4219 16.7063C6.4424 16.7233 6.46379 16.7391 6.48596 16.7539L6.48607 16.754C6.48614 16.754 6.48621 16.7541 6.48628 16.7541C6.50824 16.7687 6.53101 16.7823 6.55449 16.7947C6.55537 16.7952 6.55626 16.7956 6.55711 16.7961C6.57914 16.8077 6.60176 16.8183 6.62496 16.8277C6.62687 16.8285 6.62879 16.8292 6.63066 16.83C6.65244 16.8387 6.67465 16.8466 6.69735 16.8533C6.70025 16.8542 6.70315 16.8549 6.70606 16.8558C6.72752 16.8619 6.7493 16.8673 6.77143 16.8716C6.77519 16.8723 6.77894 16.8729 6.78269 16.8736C6.80391 16.8774 6.8254 16.8805 6.84718 16.8826C6.85133 16.883 6.85547 16.8832 6.85961 16.8836C6.88065 16.8853 6.90186 16.8864 6.92336 16.8864H6.92584C6.92683 16.8864 6.92778 16.8864 6.92874 16.8864H7.81533L7.05573 18.0258C6.81421 18.3881 6.91213 18.8775 7.27438 19.119C7.63666 19.3605 8.12612 19.2626 8.3676 18.9004L9.94076 16.5407C9.94189 16.539 9.94299 16.5373 9.94412 16.5356L9.9443 16.5354L9.94441 16.5352C9.9591 16.5132 9.97263 16.4904 9.9851 16.4669C9.98556 16.4661 9.98595 16.4653 9.98641 16.4644C9.99799 16.4424 10.0086 16.4197 10.0181 16.3965C10.0189 16.3946 10.0196 16.3927 10.0204 16.3908C10.0291 16.369 10.0369 16.3468 10.0437 16.3241C10.0445 16.3212 10.0453 16.3183 10.0462 16.3154C10.0523 16.294 10.0576 16.2722 10.062 16.25C10.0627 16.2463 10.0633 16.2425 10.064 16.2387C10.0678 16.2175 10.0709 16.196 10.073 16.1743C10.0734 16.1701 10.0736 16.166 10.074 16.1618C10.0757 16.1408 10.0768 16.1196 10.0768 16.0981C10.0768 16.0975 10.0767 16.0971 10.0767 16.0965C10.0766 16.0936 10.0765 16.0907 10.0764 16.0879Z"
      fill={props?.fill || "white"}
    />
  </Svg>
);

const Plus: React.FC<SvgProps> = ({ color = "#FFFFFF", width = 7, height = 7, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 7 7" fill="none" {...props}>
    <Path
      d="M6.67636 3.96655H4.032V6.67636H2.64436V3.96655H0V2.70982H2.64436V0H4.032V2.70982H6.67636V3.96655Z"
      fill={color || "white"}
    />
  </Svg>
);

const ForwardArrowBold = (props: SvgProps) => (
  <Svg width="20" height="15" viewBox="0 0 20 15" fill="none">
    <Path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.95682 7.24963C0.956921 7.087 1.02157 6.93106 1.13657 6.81606C1.25157 6.70107 1.4075 6.63642 1.57014 6.63632L17.4469 6.63632L11.9849 1.17434C11.8698 1.05925 11.8052 0.903149 11.8052 0.740387C11.8052 0.577624 11.8698 0.421528 11.9849 0.306437C12.1 0.191346 12.2561 0.126688 12.4189 0.126688C12.5816 0.126688 12.7377 0.191346 12.8528 0.306437L19.3621 6.81568C19.4772 6.93077 19.5418 7.08687 19.5418 7.24963C19.5418 7.41239 19.4772 7.56849 19.3621 7.68358L12.8528 14.1928C12.7377 14.3079 12.5816 14.3726 12.4189 14.3726C12.2561 14.3726 12.1 14.3079 11.9849 14.1928C11.8698 14.0777 11.8052 13.9216 11.8052 13.7589C11.8052 13.5961 11.8698 13.44 11.9849 13.3249L17.4469 7.86295L1.57014 7.86295C1.40751 7.86285 1.25156 7.7982 1.13657 7.6832C1.02157 7.5682 0.956921 7.41226 0.95682 7.24963Z"
      fill="white"
    />
  </Svg>
);

const FoodHexagon: React.FC<SvgProps> = ({ width = 40, height = 43, ...props }) => (
  <Svg width={width} height={height} fill="none" {...props}>
    <Path
      stroke={props?.color || "#fff"}
      strokeWidth={1.515}
      d="m20.573 1.344-.378-.218-.379.218-16.887 9.75-.379.219v20.374l.379.219 16.887 9.75.379.218.378-.218 16.888-9.75.379-.219V11.313l-.379-.219-16.887-9.75Z"
    />
    <Path stroke={props?.color || "#fff"} strokeWidth={0.379} d="m33.922 13.902-14.01-7.951" />
    <Path
      stroke={props?.color || "#fff"}
      strokeWidth={1.529}
      d="m33.638 14.403-14.01-7.952m14.71 23.473-13.95 8.055M6.367 29.847V13.738"
    />
    <Path
      fill={props?.color || "#fff"}
      d="M17.674 16.945c-.248 0-.414.248-.414.414v2.07h-.828v-2.07c0-.249-.166-.415-.414-.415-.248 0-.414.249-.414.415v2.07h-.829v-2.07c0-.249-.165-.415-.414-.415-.248 0-.414.249-.414.415v2.733c0 .745.58 1.325 1.243 1.408v5.798c0 .497.331.828.828.828.497 0 .828-.331.828-.828V21.5a1.432 1.432 0 0 0 1.243-1.408v-2.733c0-.166-.166-.415-.415-.415Zm1.657.414v4.97h.828v4.969c0 .497.332.828.829.828.497 0 .828-.331.828-.828V14.874c-1.408 0-2.485 1.076-2.485 2.485Zm5.798-.829c-1.16 0-2.07 1.243-2.07 2.734-.084.994.413 1.905 1.242 2.485v5.549c0 .497.331.828.828.828.497 0 .828-.331.828-.828v-5.55c.829-.58 1.325-1.49 1.243-2.484 0-1.491-.911-2.734-2.071-2.734Z"
    />
  </Svg>
);

const Avatar: React.FC<SvgProps> = ({ width = 48, height = 48, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 45 52" fill="none" {...props}>
    <Path
      d="M22.4997 19.9838C28.0181 19.9838 32.4916 15.5103 32.4916 9.9919C32.4916 4.47352 28.0181 0 22.4997 0C16.9813 0 12.5078 4.47352 12.5078 9.9919C12.5078 15.5103 16.9813 19.9838 22.4997 19.9838Z"
      fill="#9C9B9B"
    />
    <Path
      d="M36.248 51.0536H8.75203C6.43084 51.0536 4.20473 50.1315 2.56341 48.4901C0.922086 46.8488 0 44.6227 0 42.3015C0.00482731 37.3318 1.98118 32.567 5.4953 29.0529C9.00942 25.5388 13.7742 23.5624 18.7439 23.5576H26.2561C31.2258 23.5624 35.9906 25.5388 39.5047 29.0529C43.0188 32.567 44.9952 37.3318 45 42.3015C45 44.6227 44.0779 46.8488 42.4366 48.4901C40.7953 50.1315 38.5692 51.0536 36.248 51.0536Z"
      fill="#9C9B9B"
    />
  </Svg>
);

const LogoIcon: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width="60" height="43" viewBox="0 0 60 43" fill="none">
    <Path
      d="M34.2742 42.1408H24.6573C23.1974 41.9619 21.8316 41.3025 20.6825 40.257C19.5333 39.2209 18.5914 37.8081 17.9792 36.122L12.5162 21.0704L7.0531 6.01877C5.9134 2.87281 3.61516 0.668752 0.940149 0.0941905C0.751768 0.0565143 0.563381 0.0188381 0.375 0H10.9338C13.8537 0.357924 16.3968 2.64675 17.6119 6.01877L23.0749 21.0704L28.538 36.122C29.6306 39.1267 31.7781 41.2837 34.3024 41.9619C34.406 41.9901 34.3872 42.1408 34.2742 42.1408Z"
      fill="url(#paint0_linear_246_634)"
      fillOpacity="0.16"
    />
    <Path
      d="M24.6574 42.1408H25.1189H35.2067C36.6667 41.9619 38.0324 41.3025 39.1815 40.257C40.3307 39.2209 41.2726 37.8081 41.8848 36.122L47.3479 21.0704L52.8109 6.01877C53.9789 2.80688 56.3431 0.574562 59.084 0.0659333C59.2159 0.0376762 59.3571 0.0188381 59.489 0H59.1499H48.9303C46.0103 0.357924 43.4672 2.64675 42.2522 6.01877L36.7891 21.0704L31.3261 36.122C30.1675 39.3057 27.8222 41.5286 25.1095 42.0655C24.9588 42.0937 24.7987 42.122 24.6479 42.1408H24.6574Z"
      fill="url(#paint1_linear_246_634)"
      fillOpacity="0.16"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_246_634"
        x1="17.3575"
        y1="-0.423857"
        x2="17.3575"
        y2="40.1722"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#13689E" />
        <Stop offset="0.74" stopColor="#E5005F" />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_246_634"
        x1="42.0826"
        y1="0.0470952"
        x2="42.0826"
        y2="39.6071"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#23B8B1" />
        <Stop offset="0.2" stopColor="#57B885" />
        <Stop offset="0.48" stopColor="#9DB94C" />
        <Stop offset="0.72" stopColor="#CFBA23" />
        <Stop offset="0.9" stopColor="#EFBA09" />
        <Stop offset="1" stopColor="#FBBB00" />
      </LinearGradient>
    </Defs>
  </Svg>
);

const SaveArrow: React.FC<SvgProps> = ({ color = "#13689E", width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 18 17" fill="none" {...props}>
    <Path
      d="M13.4902 8.44436L17.2402 12.1944M17.2402 12.1944L13.4902 15.9444M17.2402 12.1944H0.740234V0.943359"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const Scan: React.FC<SvgProps> = ({
  color = config.colors.white, // Default color
  width = 100,
  height = 100,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 42 42" fill="none" {...props}>
    <G fill="#fff">
      <Path d="M21 5.24c8.69 0 15.76 7.07 15.76 15.76S29.69 36.76 21 36.76 5.24 29.69 5.24 21 12.31 5.24 21 5.24m0-1c-9.26 0-16.76 7.5-16.76 16.76S11.74 37.76 21 37.76 37.76 30.26 37.76 21 30.26 4.24 21 4.24Z" />
      <Path d="M20.12 22.85c1.2-.41 1.79-1.72 1.39-2.87a.56.56 0 0 1 .28-.68 2.037 2.037 0 0 0 .54-3.29.556.556 0 0 1 .07-.85c.91-.62 1.27-1.82.86-2.84-.12-.3-.3-.57-.52-.79a.553.553 0 0 1-.12-.61 3.33 3.33 0 0 0-1.65-4.34c-1.62-.75-3.59-.06-4.38 1.54-.1.2-.32.32-.54.3-.32-.03-.64 0-.94.11a2.388 2.388 0 0 0-1.62 2.49c.04.35-.25.64-.6.6a1.99 1.99 0 0 0-1.56.52c-.56.51-.79 1.28-.6 2.01.07.27-.08.55-.34.65a2.244 2.244 0 0 0-1.3 2.91c1.29 3.36 1.26 5.71 1.31 6.1.22 3-1.02 5.42-1.04 5.46a.963.963 0 0 0 .46 1.27c.48.22 1.05.01 1.27-.46.05-.08 1-2.49 3.51-4.32 1.66-1.21 3.52-2.24 5.51-2.93Zm-2.46-1.44c.07.3-.11.59-.4.66l-3.24.79-.84 1.8c-.13.28-.46.4-.73.27a.547.547 0 0 1-.27-.73l.83-1.8c-.37-.72-1.19-2.36-1.42-2.85-.21-.45-.08-.74.18-.87.3-.16.67 0 .78.32.08.19.58 1.18 1.06 2.13l1.07-2.3c-.36-.71-1.19-2.36-1.42-2.85-.21-.45-.08-.75.2-.88.3-.14.65.02.75.33.08.19.58 1.18 1.06 2.13l1.07-2.3c-.37-.72-1.19-2.36-1.42-2.85-.21-.45-.08-.75.19-.88.3-.15.66 0 .76.33.08.19.58 1.18 1.06 2.13l.94-2.03c.13-.28.46-.4.73-.27.28.13.4.46.27.73l-.94 2.02 2.38-.58a.545.545 0 1 1 .26 1.06l-3.24.79L16.27 18l2.38-.58a.545.545 0 1 1 .26 1.06l-3.24.79-1.06 2.29 2.38-.58c.3-.07.59.11.67.4ZM24.04 27.72c0-.1-.04-.19-.11-.26a.363.363 0 0 0-.52 0c-.09.09-.21.45-.29.82.37-.09.73-.21.82-.29.07-.07.11-.16.11-.26ZM19.19 31.83c0 .1.04.19.11.26.14.14.39.14.53 0 .09-.09.21-.45.29-.82-.37.09-.73.21-.82.29-.07.07-.11.16-.11.26ZM23.11 31.27c.09.37.21.73.29.82.14.14.39.14.53 0a.363.363 0 0 0 0-.52c-.09-.09-.45-.21-.82-.29ZM23.73 29.77c.32.2.66.37.79.37a.37.37 0 1 0 0-.74c-.12 0-.47.17-.79.37ZM19.82 27.45a.363.363 0 0 0-.52 0 .363.363 0 0 0 0 .52c.09.09.45.21.82.29-.09-.37-.21-.73-.29-.82ZM18.33 29.77c0 .21.17.37.37.37.12 0 .47-.17.79-.37-.32-.2-.66-.37-.79-.37a.37.37 0 0 0-.37.37ZM21.61 26.5a.37.37 0 0 0-.37.37c0 .12.17.47.37.79.2-.32.37-.66.37-.79a.37.37 0 0 0-.37-.37Z" />
      <Path d="M21.61 24.72c-2.78 0-5.05 2.27-5.05 5.05s2.27 5.05 5.05 5.05 5.05-2.27 5.05-5.05-2.27-5.05-5.05-5.05Zm0 9.61c-2.51 0-4.56-2.05-4.56-4.56s2.05-4.56 4.56-4.56 4.56 2.05 4.56 4.56-2.05 4.56-4.56 4.56Z" />
      <Path d="M21.24 32.68a.37.37 0 1 0 .74 0c0-.12-.17-.47-.37-.79-.2.32-.37.66-.37.79Z" />
      <Path d="M21.61 25.57a4.2 4.2 0 1 0-.001 8.401 4.2 4.2 0 0 0 .001-8.401Zm1.09 2.91c.03-.17.21-1.03.45-1.28.14-.14.32-.21.52-.21s.38.08.52.21.21.32.21.52-.08.38-.21.52c-.24.24-1.1.42-1.28.45h-.03a.21.21 0 0 1-.13-.05.184.184 0 0 1-.05-.16Zm-4.73 1.29c0-.4.33-.73.73-.73.34 0 1.08.49 1.22.58.05.03.08.09.08.15s-.03.12-.08.15c-.14.1-.88.58-1.22.58-.4 0-.73-.33-.73-.73Zm2.56 1.3c-.03.17-.21 1.03-.45 1.28-.14.14-.32.21-.52.21s-.38-.08-.52-.21-.21-.32-.21-.52.08-.38.21-.52c.24-.24 1.1-.42 1.28-.45.06-.01.12 0 .16.05.04.04.06.1.05.16Zm-.05-2.43s-.08.05-.13.05h-.03c-.17-.03-1.03-.21-1.28-.45-.14-.14-.21-.32-.21-.52s.08-.38.21-.52c.14-.14.32-.21.52-.21s.38.08.52.21c.24.24.42 1.11.45 1.28.01.06 0 .12-.05.16Zm1.13 4.77c-.4 0-.73-.33-.73-.73 0-.34.49-1.08.58-1.22.07-.1.23-.1.3 0 .1.14.58.88.58 1.22 0 .4-.33.73-.73.73Zm.15-5.32s-.09.08-.15.08-.12-.03-.15-.08c-.1-.14-.58-.88-.58-1.22 0-.4.33-.73.73-.73s.73.33.73.73c0 .34-.49 1.08-.58 1.22Zm2.42 4.25c-.14.14-.32.21-.52.21s-.38-.08-.52-.21c-.24-.24-.42-1.11-.45-1.28-.01-.06 0-.12.05-.16s.1-.06.16-.05c.17.03 1.03.21 1.28.45.14.14.21.32.21.52s-.08.38-.21.52Zm1.07-2.57c0 .4-.33.73-.73.73-.34 0-1.08-.49-1.22-.58-.05-.03-.08-.09-.08-.15s.03-.12.08-.15c.14-.1.88-.58 1.22-.58.4 0 .73.33.73.73ZM33.59 19.18c-.54-1.87-1.99-3.19-3.78-3.42-1.2-.18-2.16.13-2.87.94-.26.27-.36.66-.26 1.02.48 1.36 1.69.37 1.86.89a.31.31 0 0 1-.17.4c-.02 0-.05.02-.07.02-.84.18-1.69.34-2.52.51l-.54.11c-1.15.21-1.59 1.1-1.22 2.17.35 1.57 1.3 1.88 2.78 1.19.78-.3 1.59-.6 2.39-.9.16-.06.34.01.4.17 0 .02.02.05.02.07.03.08.08.22-.41.41-.46.15-.76.59-.74 1.07.03.47.35.86.81.98 1.03.31 2.01.07 2.93-.72 1.4-1.16 1.94-3.04 1.4-4.91Zm-1.21 3.12a.164.164 0 0 1-.07-.22c.77-1.54.13-3.78-1.34-4.69a.163.163 0 0 1-.05-.23c.05-.07.15-.1.22-.05 1.63 1.01 2.32 3.4 1.46 5.12-.04.08-.14.11-.22.07ZM1.98 13.47c-.39 0-.7-.31-.7-.7V4.41c0-1.73 1.4-3.13 3.13-3.13h9.01c.39 0 .7.31.7.7s-.31.7-.7.7H4.41c-.95 0-1.72.78-1.72 1.73v8.36c0 .39-.31.7-.7.7ZM13.42 40.72H4.41c-1.73 0-3.12-1.4-3.13-3.13V30.1c0-.39.31-.7.7-.7s.7.31.7.7v7.49c0 .95.77 1.72 1.72 1.72h9.01c.39 0 .7.31.7.7s-.31.7-.7.7ZM37.6 40.72h-8.66c-.39 0-.7-.31-.7-.7s.31-.7.7-.7h8.66c.95 0 1.72-.77 1.73-1.72v-7.96c0-.39.31-.7.7-.7s.7.31.7.7v7.96c0 1.73-1.4 3.13-3.13 3.13ZM40.03 13.47c-.39 0-.7-.31-.7-.7V4.41c0-.95-.77-1.73-1.73-1.73h-8.66c-.39 0-.7-.31-.7-.7s.31-.7.7-.7h8.66c1.73 0 3.13 1.4 3.13 3.13v8.36c0 .39-.31.7-.7.7Z" />
      <Path d="M21 3.72c9.53 0 17.28 7.75 17.28 17.28S30.53 38.28 21 38.28 3.72 30.53 3.72 21 11.47 3.72 21 3.72m0-1C10.91 2.72 2.72 10.91 2.72 21S10.9 39.28 21 39.28 39.28 31.1 39.28 21 31.09 2.72 21 2.72Z" />
    </G>
  </Svg>
);

const CirclePlus: React.FC<SvgProps> = ({ color, width = 24, height = 24, strokeWidth = 2, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 40 40" fill="none" {...props}>
    <Path
      d="M20.0003 13.4572V25.9145M26.2289 19.6858H13.7717M38.6861 19.6858C38.6861 22.1397 38.2028 24.5695 37.2638 26.8366C36.3247 29.1037 34.9483 31.1636 33.2132 32.8987C31.478 34.6339 29.4181 36.0103 27.1511 36.9493C24.884 37.8884 22.4542 38.3717 20.0003 38.3717C17.5464 38.3717 15.1166 37.8884 12.8495 36.9493C10.5825 36.0103 8.52255 34.6339 6.78741 32.8987C5.05227 31.1636 3.67588 29.1037 2.73683 26.8366C1.79778 24.5695 1.31445 22.1397 1.31445 19.6858C1.31445 14.73 3.28313 9.97723 6.78741 6.47296C10.2917 2.96868 15.0445 1 20.0003 1C24.9561 1 29.7089 2.96868 33.2132 6.47296C36.7175 9.97723 38.6861 14.73 38.6861 19.6858Z"
      stroke={"white"}
      stroke-width="1.36726"
      stroke-linecap="round"
      stroke-linejoin="round"
      strokeWidth={strokeWidth}
      {...props}
    />
  </Svg>
);

const Search: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      d="M19.0008 19.0008L13.8038 13.8038M13.8038 13.8038C15.2104 12.3972 16.0006 10.4895 16.0006 8.50028C16.0006 6.51108 15.2104 4.60336 13.8038 3.19678C12.3972 1.79021 10.4895 1 8.50028 1C6.51108 1 4.60336 1.79021 3.19678 3.19678C1.79021 4.60336 1 6.51108 1 8.50028C1 10.4895 1.79021 12.3972 3.19678 13.8038C4.60336 15.2104 6.51108 16.0006 8.50028 16.0006C10.4895 16.0006 12.3972 15.2104 13.8038 13.8038Z"
      stroke="#9C9B9B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Switch: React.FC<SvgProps> = ({ color, width = 26, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 48 48" fill="none" {...props}>
    <G clip-path="url(#clip0_4505_17360)">
      <Path
        d="M11.0073 13.0888C10.9426 12.5551 11.3145 12.0701 11.8481 12.0054C12.3818 11.9406 12.8668 12.3126 12.9315 12.8462L13.5137 17.3576C15.7128 14.0913 19.4319 12.07 23.4421 12.07C28.5519 12.07 33.0957 15.3202 34.7612 20.1389C34.9391 20.6402 34.6642 21.19 34.1629 21.3678C34.0659 21.4002 33.9527 21.4163 33.8395 21.4163C33.4353 21.4163 33.0633 21.1576 32.9178 20.7695C31.5272 16.727 27.7111 14.0104 23.4422 14.0104C19.9494 14.0104 16.6992 15.8538 14.8882 18.7967L19.7392 18.247C20.2728 18.1823 20.7579 18.5704 20.8064 19.104C20.8549 19.6376 20.483 20.1227 19.9494 20.1713L12.9639 20.9636C12.9315 20.9636 12.883 20.9636 12.8507 20.9636C12.3656 20.9636 11.9451 20.6078 11.8966 20.1228L11.0073 13.0888ZM35.0361 26.0733L28.0506 26.8657C27.5169 26.9304 27.1289 27.3993 27.1935 27.9329C27.2582 28.4665 27.7271 28.8546 28.2608 28.7899L32.4812 28.321C30.8318 31.7976 27.3229 34.0452 23.4259 34.0452C19.0438 34.0452 15.0983 31.1184 13.837 26.9304C13.6753 26.4129 13.1417 26.1219 12.6243 26.2835C12.1069 26.4452 11.8158 26.9789 11.9774 27.4963C12.7051 29.9056 14.2251 32.0724 16.2302 33.5924C18.3161 35.1609 20.8063 36.0018 23.4259 36.0018C28.2123 36.0018 32.4974 33.1558 34.3892 28.8384L35.0684 34.223C35.1331 34.7081 35.5535 35.0639 36.0224 35.0639C36.0709 35.0639 36.1033 35.0639 36.1518 35.0639C36.6854 34.9992 37.0573 34.5141 36.9926 33.9805L36.0871 26.9465C36.0386 26.3967 35.5697 26.0248 35.0361 26.0733Z"
        fill="white"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_4505_17360">
        <Rect width="26" height="26" fill="white" transform="matrix(1 0 0 -1 11 37)" />
      </ClipPath>
    </Defs>
  </Svg>
);

const Flash: React.FC<SvgProps> = ({ color, width = 32, height = 32, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 32 32" fill="none" {...props}>
    <Path
      d="M12.2402 24.0535V27.3068C12.2402 29.5468 13.4536 30.0001 14.9336 28.3201L25.0269 16.8535C26.2669 15.4535 25.7469 14.2935 23.8669 14.2935H22.6269"
      stroke="white"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <Path
      d="M13.3067 7.94653L6.95999 15.1599C5.71999 16.5599 6.23999 17.7199 8.11999 17.7199H12.24V19.2932"
      stroke="white"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <Path
      d="M19.7597 11.7867V4.69336C19.7597 2.45336 18.5464 2.00003 17.0664 3.68003"
      stroke="white"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <Path
      d="M29.3337 2.66675L2.66699 29.3334"
      stroke="white"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Funnel: React.FC<SvgProps> = ({ color, width = 24, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 12 12" fill="none" {...props}>
    <Path
      d="M6 0.750002C7.60708 0.750002 9.18208 0.885335 10.7151 1.1455C11.026 1.198 11.25 1.46984 11.25 1.78484V2.39383C11.25 2.5662 11.2161 2.73687 11.1501 2.89611C11.0841 3.05535 10.9875 3.20004 10.8656 3.32192L7.69692 6.49058C7.57504 6.61246 7.47836 6.75715 7.4124 6.91639C7.34645 7.07563 7.3125 7.24631 7.3125 7.41867V9.12608C7.31255 9.3699 7.24468 9.60891 7.1165 9.81631C6.98833 10.0237 6.80492 10.1913 6.58683 10.3003L4.6875 11.25V7.41867C4.6875 7.24631 4.65355 7.07563 4.5876 6.91639C4.52164 6.75715 4.42496 6.61246 4.30308 6.49058L1.13442 3.32192C1.01254 3.20004 0.915863 3.05535 0.849904 2.89611C0.783946 2.73687 0.749999 2.5662 0.75 2.39383V1.78484C0.75 1.46984 0.974 1.198 1.28492 1.1455C2.84275 0.88175 4.42 0.74945 6 0.750002Z"
      stroke="white"
      stroke-width="0.875"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const ChevronIn: React.FC<SvgProps> = ({ color, width = 16, height = 24, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 8 12" fill="none" {...props}>
    <Path
      fill={color || "#fff"}
      d="M7.85 6.007a.951.951 0 0 1-.083.398c-.05.122-.133.241-.25.357l-4.98 4.923a.887.887 0 0 1-.664.274.952.952 0 0 1-.689-.274.942.942 0 0 1-.274-.681c0-.266.1-.5.299-.706l4.358-4.291-4.358-4.292A.983.983 0 0 1 .91 1.01c0-.266.091-.49.274-.673a.937.937 0 0 1 .69-.282c.265 0 .486.091.663.274l4.98 4.922c.222.216.333.468.333.756Z"
    />
  </Svg>
);

const Pill2: React.FC<SvgProps> = ({
  color = "#D9D9D9", // Default color for the pill
  width = 71,
  height = 33,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 71 33" fill="none" {...props}>
    <Path
      d="M0.945312 16.6908C0.945312 7.76828 8.00865 0.535156 16.7217 0.535156H55.1767C63.8898 0.535156 70.9531 7.76828 70.9531 16.6908C70.9531 25.6133 63.8898 32.8464 55.1767 32.8464H16.7217C8.00865 32.8464 0.945312 25.6133 0.945312 16.6908Z"
      fill={color} // Use dynamic color or default
    />
  </Svg>
);

const NoMedication: React.FC<SvgProps> = ({ color = "#D9D9D9", width = 60, height = 53, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 60 53" fill="none" {...props}>
    <Path
      d="M46.2549 4.375L45.6299 3.75C43.4424 1.25 40.0049 0 36.8799 0C33.7549 0 30.3174 1.25 27.8174 3.75L3.75488 27.8125C-1.24512 32.8125 -1.24512 40.625 3.75488 45.625L4.37988 46.25C6.56738 48.75 10.0049 50 13.1299 50C16.2549 50 19.6924 48.75 22.1924 46.25L46.5674 21.875C51.2549 17.1875 51.2549 9.0625 46.2549 4.375ZM44.0674 20L31.8799 32.1875L20.9424 20.9375L9.06738 32.8125C5.62988 36.25 5.62988 40.625 5.94238 43.75C2.19238 40 2.19238 34.0625 5.94238 30.3125L30.3174 5.9375C31.8799 4.0625 34.3799 3.125 36.8799 3.125C39.3799 3.125 41.8799 4.0625 43.7549 5.9375L44.3799 6.5625C45.9424 8.125 46.8799 10.625 46.8799 13.125C46.8799 15.625 45.9424 18.125 44.0674 20Z"
      fill="#444444"
    />
    <Path
      d="M45.7451 38.75L50.2451 43.25M50.2451 38.75L45.7451 43.25M56.9951 41C56.9951 42.1819 56.7623 43.3522 56.31 44.4442C55.8577 45.5361 55.1948 46.5282 54.3591 47.364C53.5234 48.1997 52.5312 48.8626 51.4393 49.3149C50.3473 49.7672 49.177 50 47.9951 50C46.8132 50 45.6429 49.7672 44.551 49.3149C43.459 48.8626 42.4669 48.1997 41.6312 47.364C40.7954 46.5282 40.1325 45.5361 39.6802 44.4442C39.2279 43.3522 38.9951 42.1819 38.9951 41C38.9951 38.6131 39.9433 36.3239 41.6312 34.636C43.319 32.9482 45.6082 32 47.9951 32C50.3821 32 52.6713 32.9482 54.3591 34.636C56.0469 36.3239 56.9951 38.6131 56.9951 41Z"
      stroke="#505050"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const Drop: React.FC<SvgProps> = ({
  color = "#D9D9D9", // Default color for the pill
  width = 10,
  height = 14,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 10 14" fill="none" {...props}>
    <Path
      d="M5 0L4.375 0.714459C4.375 0.714459 3.30833 1.94775 2.23333 3.54678C1.15833 5.14581 0 7.05954 0 8.89672C0 10.2502 0.526784 11.5482 1.46447 12.5053C2.40215 13.4623 3.67392 14 5 14C6.32608 14 7.59785 13.4623 8.53553 12.5053C9.47322 11.5482 10 10.2502 10 8.89672C10 7.05954 8.84167 5.14581 7.76667 3.54678C6.69167 1.94775 5.625 0.714459 5.625 0.714459L5 0ZM5 2.66221C5.36667 3.1045 5.7 3.47023 6.4 4.5079C7.40833 6.00486 8.33333 7.91009 8.33333 8.89672C8.33333 10.7849 6.85 12.2989 5 12.2989C3.15 12.2989 1.66667 10.7849 1.66667 8.89672C1.66667 7.91009 2.59167 6.00486 3.6 4.5079C4.3 3.47023 4.63333 3.1045 5 2.66221Z"
      fill="white"
    />
  </Svg>
);

const RightArrow: React.FC<SvgProps> = ({ color = "#D9D9D9", width = 20, height = 15, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 20 15" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.95682 7.24966C0.956921 7.08703 1.02157 6.93109 1.13657 6.8161C1.25157 6.7011 1.4075 6.63645 1.57014 6.63635L17.4469 6.63635L11.9849 1.17437C11.8698 1.05928 11.8052 0.90318 11.8052 0.740418C11.8052 0.577655 11.8698 0.421558 11.9849 0.306468C12.1 0.191377 12.2561 0.126719 12.4189 0.126719C12.5816 0.126719 12.7377 0.191377 12.8528 0.306468L19.3621 6.81571C19.4772 6.9308 19.5418 7.0869 19.5418 7.24966C19.5418 7.41242 19.4772 7.56852 19.3621 7.68361L12.8528 14.1929C12.7377 14.3079 12.5816 14.3726 12.4189 14.3726C12.2561 14.3726 12.1 14.3079 11.9849 14.1929C11.8698 14.0778 11.8052 13.9217 11.8052 13.7589C11.8052 13.5961 11.8698 13.44 11.9849 13.325L17.4469 7.86298L1.57014 7.86298C1.40751 7.86288 1.25156 7.79823 1.13657 7.68323C1.02157 7.56823 0.956921 7.41229 0.95682 7.24966Z"
      fill={color || "white"}
    />
  </Svg>
);
const Check: React.FC<SvgProps> = ({
  color = "#04C17D", // Default color for the pill
  width = 20,
  height = 20,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 21" fill="none" {...props}>
    <Path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0 10.5C0 7.84784 1.05357 5.3043 2.92893 3.42893C4.8043 1.55357 7.34784 0.5 10 0.5C12.6522 0.5 15.1957 1.55357 17.0711 3.42893C18.9464 5.3043 20 7.84784 20 10.5C20 13.1522 18.9464 15.6957 17.0711 17.5711C15.1957 19.4464 12.6522 20.5 10 20.5C7.34784 20.5 4.8043 19.4464 2.92893 17.5711C1.05357 15.6957 0 13.1522 0 10.5ZM9.42933 14.78L15.1867 7.58267L14.1467 6.75067L9.23733 12.8853L5.76 9.988L4.90667 11.012L9.42933 14.78Z"
      fill={color}
    />
  </Svg>
);

const Formula: React.FC<SvgProps> = ({ color = "#D9D9D9", width = 17, height = 25, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 17 25" {...props}>
    <Path
      d="M13.6243 8.06343V6.86336H14.6491C15.2151 6.86336 15.674 6.4045 15.674 5.8385V1.73914C15.674 1.17315 15.2152 0.714287 14.6491 0.714287H2.35097C1.78498 0.714287 1.32612 1.17315 1.32612 1.73914V5.8385C1.32612 6.4045 1.78498 6.86336 2.35097 6.86336H3.37583V8.06343C1.59858 8.58799 0.30127 10.2321 0.30127 12.1792V19.9947C0.30127 22.3644 2.22258 24.2857 4.59229 24.2857H12.4078C14.7775 24.2857 16.6988 22.3644 16.6988 19.9947V12.1792C16.6988 10.2321 15.4015 8.58799 13.6243 8.06343ZM3.37583 2.76399H13.6243V4.8137H12.5995H4.40068H3.37583V2.76399ZM11.5746 6.86336V7.88821H5.42549V6.86336H11.5746ZM14.6491 19.9947C14.6491 21.2325 13.6455 22.2361 12.4078 22.2361H4.59229C3.35456 22.2361 2.35093 21.2325 2.35093 19.9947V12.1792C2.35093 10.9415 3.35456 9.93787 4.59229 9.93787H12.4078C13.6455 9.93787 14.6491 10.9415 14.6491 12.1792V19.9947Z"
      fill="white"
    />
  </Svg>
);

export const XML_PILL = `<svg width="59" height="58" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="29.4455" cy="28.9455" r="28.9455" fill="#13689E"/><g filter="url(#a)"><path d="M42.0798 29.7899c0 7.0636-5.7263 12.7899-12.7899 12.7899-7.0637 0-12.7899-5.7263-12.7899-12.7899C16.5 22.7262 22.2262 17 29.2899 17c7.0636 0 12.7899 5.7262 12.7899 12.7899Z" fill="#D9D9D9"/></g><defs><filter id="a" x="16.5" y="17" width="25.5796" height="28.2724" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2.69261"/><feGaussianBlur stdDeviation="1.3463"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_4804_13169"/></filter></defs></svg>`;

export const PILL_SHAPES_ENUM = {
  1: PillShape1,
  2: PillShape2,
  3: PillShape3,
  4: PillShape4,
  5: PillShape5,
  6: PillShape6,
  7: PillShape7,
  8: PillShape8,
  9: PillShape9,
  10: PillShape10,
};

const UnCheck: React.FC<SvgProps> = ({ width = 20, height = 20, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 20 21" fill="none" {...props}>
    <Circle cx="10" cy="10.5" r="9" stroke={props?.color || "white"} stroke-width="2" />
  </Svg>
);

const WhiteTick: React.FC<SvgProps> = ({ color = "#D9D9D9", width = 17, height = 44, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 17 44" fill="none">
    <Path
      d="M17 17.2617L5.34286 28L0 23.0783L1.36971 21.8166L5.34286 25.4676L15.6303 16L17 17.2617Z"
      fill={color || "white"}
    />
  </Svg>
);

const CopyFile: React.FC<SvgProps> = ({ color = "#D9D9D9", width = 5, height = 5, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 13 15" fill="none" {...props}>
    <Path
      d="M9 11V13.25C9 13.664 8.664 14 8.25 14H1.75C1.55109 14 1.36032 13.921 1.21967 13.7803C1.07902 13.6397 1 13.4489 1 13.25V4.75C1 4.336 1.336 4 1.75 4H3C3.33505 3.99977 3.66954 4.02742 4 4.08267M9 11H11.25C11.664 11 12 10.664 12 10.25V7C12 4.02667 9.838 1.55933 7 1.08267C6.66954 1.02742 6.33505 0.999773 6 1H4.75C4.336 1 4 1.336 4 1.75V4.08267M9 11H4.75C4.55109 11 4.36032 10.921 4.21967 10.7803C4.07902 10.6397 4 10.4489 4 10.25V4.08267M12 8.5V7.25C12 6.65326 11.7629 6.08097 11.341 5.65901C10.919 5.23705 10.3467 5 9.75 5H8.75C8.55109 5 8.36032 4.92098 8.21967 4.78033C8.07902 4.63968 8 4.44891 8 4.25V3.25C8 2.95453 7.9418 2.66195 7.82873 2.38896C7.71566 2.11598 7.54992 1.86794 7.34099 1.65901C7.13206 1.45008 6.88402 1.28435 6.61104 1.17127C6.33806 1.0582 6.04547 1 5.75 1H5"
      stroke={color}
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const ArrowLink: React.FC<SvgProps> = ({ color = "#9C9B9B", width = 5, height = 5, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 11 10" fill="none" {...props}>
    <Path
      d="M1.50049 9L9.50049 1M9.50049 1H3.50049M9.50049 1V7"
      stroke={"#9C9B9B"}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

const Clock: React.FC<SvgProps> = ({ color = "#9C9B9B", width = 5, height = 5, ...props }) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      d="M10 4V10H14.5M19 10C19 11.1819 18.7672 12.3522 18.3149 13.4442C17.8626 14.5361 17.1997 15.5282 16.364 16.364C15.5282 17.1997 14.5361 17.8626 13.4442 18.3149C12.3522 18.7672 11.1819 19 10 19C8.8181 19 7.64778 18.7672 6.55585 18.3149C5.46392 17.8626 4.47177 17.1997 3.63604 16.364C2.80031 15.5282 2.13738 14.5361 1.68508 13.4442C1.23279 12.3522 1 11.1819 1 10C1 7.61305 1.94821 5.32387 3.63604 3.63604C5.32387 1.94821 7.61305 1 10 1C12.3869 1 14.6761 1.94821 16.364 3.63604C18.0518 5.32387 19 7.61305 19 10Z"
      stroke={color || "white"}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);


export default {
  Logo,
  LogoDark,
  Google,
  Facebook,
  Microsoft,
  Apple,
  HomeTabIcon,
  TaskTabIcon,
  LabTabIcon,
  DietTabIcon,
  Hamburger,
  FoodHexagon,
  BackArrow,
  Chevron,
  Profile,
  ForwardArrow,
  Mail,
  Support,
  Phone,
  Pencil,
  Camera,
  Close,
  Info,
  Contacts,
  Resources,
  CheckBoard,
  MedicationPill,
  Calendar,
  FormulaBottle,
  ForwardArrowBold,
  Plus,
  Avatar,
  LogoIcon,
  SaveArrow,
  Scan,
  CirclePlus,
  Search,
  Switch,
  Flash,
  Funnel,
  ChevronIn,
  Pill2,
  NoMedication,
  RightArrow,
  Formula,
  PillShape1,
  PillShape2,
  PillShape3,
  PillShape4,
  PillShape5,
  PillShape6,
  PillShape7,
  PillShape8,
  PillShape9,
  PillShape10,
  TaskClock,
  Delete,
  Drop,
  Check,
  UnCheck,
  WhiteTick,
  FormulaIcon,
  SumaryFormulaIcon,
  CloseWhite,
  PlayIcon,
  CheckboxChecked,
  UncheckedCheckbox,
  CheckboxUnChecked,
  BellIcon,
  AvatarLight,
  FormulaDarkIcon,
  TaskClockDark,
  MedPillIcon,
  MedPillDark,
  VideoWhiteIcon,
  VideoIcon,
  CopyFile,
  ArrowLink,
  FlashOnIcon,
  CameraFrame,
  FlashOffIcon,
  Clock,
  WideRibbonIcon,
  PatientIcon,
  HCPIcon,
  CarerIcon,
  TopBackground,
  BottomBackground,
  Email,
  Warning,
  WarningLight
};
