// src/api/userAPI.ts
import { User, UserOnboarding } from "@/types/schemas/user";
import axiosInstance from "../axiosInstance";
import { store } from "@/store";
import { onlyUpdateTimeZone } from "@/store/slices/userSlice";

/**
 * Validates if a string is a valid URL
 * @param url - The string to validate as a URL
 * @returns boolean - True if the string is a valid URL, false otherwise
 */
const isValidUrl = (url: string): boolean => {
  try {
    // Check if the URL has a protocol (http:// or https://)
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }

    // Try to create a URL object - this will throw an error if the URL is invalid
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

export const getUserProfile = async () => {
  try {
    const response = await axiosInstance.get("/Users");
    return response?.data?.[0];
  } catch (error) {
    throw error;
  }
};

export const getUserById = async () => {
  try {
    const response = await axiosInstance.get("/Users");
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateUserProfile = async (userData: User) => {
  try {
    const response = await axiosInstance.patch(`/Users`, userData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const userOnboarding = async (userData: UserOnboarding) => {
  try {
    const response = await axiosInstance.patch(`/users/UserOnBoard`, userData);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateUserProfileByPatch = async (profileData: {
  id: number;
  url: string;
}) => {
  try {
    const response = await axiosInstance.patch(
      `/Users/<USER>
      profileData
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createUser = async (userData: User) => {
  try {
    const response = await axiosInstance.post("/Users", userData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteUser = async () => {
  try {
    await axiosInstance.delete(`/Users`);
  } catch (error) {
    throw error;
  }
};

export const getPreSignedUrl = async (fileName: string) => {
  try {
    const response = await axiosInstance.get(`/Users/<USER>
      params: {
        fileName,
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const checkPushStatus = async (token: string) => {
  try {
    const response = await axiosInstance.get(
      `/notification/CheckPushStatus/${token}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateSimplifiedDiet = async (isSimplifiedDiet: boolean) => {
  try {
    const response = await axiosInstance.patch(`/Users/<USER>
      isSimplifiedDiet,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getFeatureUpdate = async (version: string) => {
  try {
    const response = await axiosInstance.get("/FeatureUpdate", {
      params: { version },
    });
    const processedResponse = await processImageReferences(response.data);
    return processedResponse;
  } catch (error) {
    throw error;
  }
};

/**
 * Processes API responses containing image references
 * @param response - The API response object to process
 * @returns The processed response with valid image URLs
 */
export const processImageReferences = async <T extends { pictureUrl?: string }>(response: T): Promise<T> => {
  // Check if the API request was successful
  if (!response) {
    return response;
  }

  // Check if the response contains a pictureUrl property
  if (!response.pictureUrl) {
    return response;
  }

  // Check if the pictureUrl is already a valid URL
  if (isValidUrl(response.pictureUrl)) {
    return response;
  }

  // If pictureUrl is not a valid URL but contains a filename, get a pre-signed URL
  try {
    const preSignedUrl = await getPreSignedUrl(response.pictureUrl);

    // Return the modified response with the valid URL
    return {
      ...response,
      pictureUrl: preSignedUrl
    };
  } catch (error) {
    console.error('Error getting pre-signed URL:', error);
    return response; // Return the original response if there's an error
  }
};

/**
 * Represents a time zone object returned from the API.
 */
export interface TimeZone {
  id: string;
  displayName: string;
  offsetMinutes: number;
}

/**
 * Fetches available time zones from the API.
 * @param offset - (Optional) The timezone offset in minutes (e.g., 660 for UTC+11). If not provided, returns all time zones.
 * @returns An array of time zone objects from the API.
 */
export const getTimeZones = async (offset?: number): Promise<TimeZone[]> => {
  try {
    const response = await axiosInstance.get("/Users/<USER>", {
      params: offset !== undefined ? { offset } : undefined,
    });
    return response.data?.value;
  } catch (error) {
    throw error;
  }
};

/**
 * Updates the user's timezone via PATCH /Users/<USER>
 * @param payload - The payload containing the new timezoneId string
 * @returns The updated user object or API response
 */
export const updateUserTimeZone = async (payload: { timezoneId: string }) => {
  try {
    const response = await axiosInstance.patch("/Users/<USER>", payload);
    return response.data;
  } catch (error) {
    throw error;
  }
}; 