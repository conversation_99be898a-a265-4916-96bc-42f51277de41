/** @format */

import { Fonts } from "@/constants";
import { config, SCREEN_WIDTH, SCREEN_HEIGHT } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getInitialStyle = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
      paddingTop: 0,
      justifyContent: "space-between",
      paddingHorizontal: "32@ms",
    },
    loginText: {
      textAlign: "center",
      marginVertical: "80@vs",
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    authText: {
      textAlign: "center",
      marginLeft: "10@ms",
      letterSpacing: -0.01,
      color: config.colors.black,
      fontFamily: Fonts.RALEWAY_SEMI_BOLD,
    },
    row: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
    },
    btnGroupView: {},

    logoFill: {
      color: theme.colors.black,
    },
    baseView: {
      minWidth: "202@ms",
      flexDirection: "row",
      justifyContent: "flex-start",
      alignItems: "center",
      marginleft: "18@ms",
    },
    topSection: {
      paddingTop: "35@vs",
      alignItems: "center",
      justifyContent: "flex-start",
    },
    ribbon: {
      position: "absolute",
      left: 0,
      right: 0,
      top: 0,
      zIndex: -1,
    },
    middleSection: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: "150@ms",
      marginBottom: "70@ms",
    },
    imagePlaceholder: {
      marginTop: -50,
      alignItems: "center",
      width: SCREEN_WIDTH,
      flex: 1,
    },
    lottieView: {
      width: SCREEN_WIDTH * 2,
      height: SCREEN_WIDTH * 0.8,
    },

    tagline: {
      fontSize: 22,
      color: "white",
      fontFamily: Fonts.RALEWAY_BOLD,
      textAlign: "center",
    },
    bottomSection: {
      marginBottom: "80@ms",
    },
    loginButtonText: {
      color: theme.colors.white,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "17@ms",
      marginBottom: -2,
    },
    createAccountButton: {
      borderRadius: ms(16),
      height: ms(47),
    },
    createButtonText: {
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "17@ms",
      marginBottom: -2,
    },
    subText: {
      marginBottom: ms(10),
      marginTop: ms(22),
      alignSelf: "center",
      fontSize: "13@ms",
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.textPrimary,
    },
  });

export default getInitialStyle;
