import { Sc<PERSON>View, <PERSON>Bar, View } from "react-native";
import React from "react";
import { useNavigation } from "@react-navigation/native";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { SCREEN_WIDTH } from "@/theme/_config";
import { useTheme } from "@/theme";
import { SCREEN_HEIGHT } from "@gorhom/bottom-sheet";

const DisclaimerScreen = () => {
  const navigation = useNavigation();
  const styles: any = useDynamicStyles(getOnboardingStyle);
  const { variant } = useTheme();

  const handleGetStarted = () => {
    navigation.navigate("RoleOptions");
  };
  
  const scrollEnabled = SCREEN_HEIGHT < 844 ? true : false

  return (
    <View style={styles.disclamerContainer}>
      <StatusBar
        backgroundColor="white"
        barStyle={variant === "dark" ? "light-content" : "dark-content"}
        translucent={false}
      />

      <ScrollView
        contentContainerStyle={styles.disclaimerScrollContainer}
        showsVerticalScrollIndicator={false}
        bounces={false}
        scrollEnabled={scrollEnabled}
      >
        <Icons.TopBackground width={"100%"} />
        <Icons.LogoDark style={styles.disclaimerlogo} />
        <View style={styles.disclaimerTitleContainer}>
          <Typography.H1 style={styles.disclaimerTitle}>
            Disclaimer
          </Typography.H1>
          <Typography.B4 style={styles.disclaimerSubTitle}>
            The information provided through this application, including any
            medical or health-related content, is intended for informational and
            educational purposes only. It is not intended as a substitute for
            professional medical advice, diagnosis, or treatment. Always seek
            the advice of a qualified healthcare provider for any questions you
            may have regarding a medical condition. Never disregard professional
            medical advice or delay in seeking it because of something you have
            read or accessed through this application.
          </Typography.B4>
        </View>

        <View style={{ width: SCREEN_WIDTH, position: "relative" }}>
          <Icons.BottomBackground
            width={SCREEN_WIDTH}
            height={SCREEN_WIDTH * 0.99}
            preserveAspectRatio="xMidYMax meet"
          />

          <View
            style={styles.disclaimerBottomView}
          >
            <Button.Yellow
              style={styles.disclaimerBtnContainer}
              onPress={handleGetStarted}
            >
              <Typography.B2 style={styles.btnText}>I understand</Typography.B2>
            </Button.Yellow>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default DisclaimerScreen;
