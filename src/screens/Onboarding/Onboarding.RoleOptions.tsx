import { View } from "react-native";
import React, { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { Button, Typography } from "@/components/atoms";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { SafeScreen } from "@/components/template";
import { Header } from "@/components/molecules";
import { useTheme } from "@/theme";
import { RoleSelector } from "@/components/molecules/AnimatedSelector";
import { ROLE_OPTIONS } from "@/constants/onbaordingSurvey";
import {
  fetchIntentionQuestionsById,
  setRole,
} from "@/store/slices/onboardingSlice";
import { useAppDispatch } from "@/store"; 
import { IRoleOptions } from "@/components/molecules/AnimatedSelector/RoleSelector";
import Loading from "@/components/atoms/Loading/Loading";

const RoleOptions = () => {
  const navigation = useNavigation();
  const { variant } = useTheme();
  const dispatch = useAppDispatch();

  const styles: any = useDynamicStyles(getOnboardingStyle);
  const [selectedRole, setSelectedRole] = useState<IRoleOptions | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  async function handleContinueBtn() {
    try {
      setIsLoading(true);
      dispatch(setRole(selectedRole));

      const response = await dispatch(
        fetchIntentionQuestionsById(selectedRole?.id)
      ).unwrap(); // optional: use unwrap to catch API errors

      if (response) {
        navigation.navigate("IntentionOptions" as never);
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  }

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleRoleSelect = (item: IRoleOptions) => {
    setSelectedRole(item);
  };
  return (
    <SafeScreen containerStyle={styles.safeAreaView}>
      {isLoading ? (
        <Loading />
      ) : (
        <>
          <View style={styles.container}>
            <Header
              title=""
              onBackPress={handleBackPress}
              isDark={variant === "dark"}
            />

            <Typography.H2 style={styles.intentionOptionsTitle}>
              {"I am a"}
            </Typography.H2>

            {ROLE_OPTIONS.map((item) => (
              <View style={styles.optionsView}>
                <RoleSelector
                  key={item.id}
                  item={item}
                  isSelected={selectedRole?.id === item.id}
                  onToggle={handleRoleSelect}
                />
              </View>
            ))}
          </View>

          <Button.Yellow
            style={styles.intentionOptionsBtnContainer}
            onPress={handleContinueBtn}
            disabled={!selectedRole?.id}
          >
            <Typography.B2 style={styles.intentionOptionBtnText}>
              Continue
            </Typography.B2>
          </Button.Yellow>
        </>
      )}
    </SafeScreen>
  );
};

export default RoleOptions;
