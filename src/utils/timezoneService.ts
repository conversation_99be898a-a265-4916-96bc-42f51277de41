import { store } from '@/store';
import { updateUserTimeZoneThunk, setProfileTimeZone, setShowTimeZoneModal } from '@/store/slices/settingsSlice';
import { fetchUserById } from '@/store/slices/userSlice';

/**
 * Timezone service that handles device vs profile timezone logic
 */
export class TimezoneService {
  /**
   * Get the current device timezone
   */
  static getDeviceTimeZone(): string {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  /**
   * Check if a timezone is empty or UTC
   */
  static isEmptyOrUTC(timezone: string | undefined | null): boolean {
    return !timezone || timezone === 'UTC' || timezone.trim() === '';
  }

  /**
   * Initialize timezone handling based on the requirements:
   * - If profile timezone is empty/UTC, use device timezone and update user
   * - If profile timezone exists, compare with device timezone and show modal if different
   */
  static async initializeTimezone(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();
    const state = store.getState();
    const devicePreTimeZone = state.settings.profileTimeZone;
    const userTimeZone = state.user.user?.timeZoneId;

    // Case 1: Profile timezone is empty or UTC
    if (this.isEmptyOrUTC(userTimeZone)) {

      // Update the profile timezone in Redux to device timezone
      if (devicePreTimeZone !== deviceTimeZone) {
        store.dispatch(setProfileTimeZone(deviceTimeZone));
      }

      // Update user timezone via API
      try {
        await store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap();
      } catch (error) {
        console.error('🕐 TimezoneService: Failed to update user timezone:', error);
      }
      return;
    }

    if (devicePreTimeZone != deviceTimeZone && userTimeZone == deviceTimeZone) {
      store.dispatch(setProfileTimeZone(deviceTimeZone));
    }

    if (devicePreTimeZone != deviceTimeZone && userTimeZone != deviceTimeZone) {
      // store.dispatch(setShowTimeZoneModal(true)); /* chore: uncomment this line after QA build */
      store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap()
    }

  }

  /**
   * Check timezone on screen focus - specifically for HomeScreen focus events
   * This method is optimized for focus events and handles the case where
   * device timezone might have changed while the app was in background
   */
  static async checkTimezoneOnFocus(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();
    const state = store.getState();
    const profileTimeZone = state.settings.profileTimeZone;
    const userTimeZone = state.user.user?.timeZoneId;
    // Only proceed if we have user data and the timezones are different
    // Only show modal if profileTimeZone !== current detected device timezone
    if (userTimeZone && (profileTimeZone !== deviceTimeZone)) {
      if (userTimeZone !== deviceTimeZone) {
        // store.dispatch(setShowTimeZoneModal(true)); /* chore: uncomment this line after QA build */
        store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap()
      }
    }
  }

  /**
   * Handle timezone change when user chooses to update to new timezone
   */
  static async updateToNewTimeZone(newTimeZone: string, isModal?: boolean, restrictFetchingUser?: boolean): Promise<void> {
    try {
      await store.dispatch(updateUserTimeZoneThunk({ timezoneId: newTimeZone, restrictFetchingUser: restrictFetchingUser })).unwrap();

      if (isModal) {
        store.dispatch(setProfileTimeZone(newTimeZone));
      }
      store.dispatch(setShowTimeZoneModal(false));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle timezone change when user chooses to keep current timezone
   * This updates the device timezone to match the detected timezone
   */
  static async keepCurrentTimeZone(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();

    // Update the profile timezone in Redux to match the detected device timezone
    store.dispatch(setProfileTimeZone(deviceTimeZone));

    // Hide the modal
    store.dispatch(setShowTimeZoneModal(false));
  }

  /**
   * Refresh user data and re-initialize timezone
   */
  static async refreshUserAndTimezone(): Promise<void> {
    try {
      await store.dispatch(fetchUserById()).unwrap();
      await this.initializeTimezone();
    } catch (error) {
      console.error('🕐 TimezoneService: Failed to refresh user and timezone:', error);
    }
  }
} 