// Remove allTimeZones and timeZonesNames. Refactor getTimeZoneLabel to accept dynamic timeZones array.

export function getTimeZoneLabel(tz: string, timeZones?: { id: string; displayName: string }[]): string {
  if (Array.isArray(timeZones) && timeZones.length > 0) {
    const found = timeZones.find((zone) => zone.id === tz);
    return found ? found.displayName : tz;
  }
  // fallback: show the IANA id if no data
  return tz;
}

export function getTimeZoneOffset(tz: string) {
  try {
    const now = new Date();
    const tzString = now.toLocaleString("en-US", { timeZone: tz });
    const tzDate = new Date(tzString);
    const localUtc = now.getTime() + now.getTimezoneOffset() * 60000;
    const tzUtc = tzDate.getTime() + tzDate.getTimezoneOffset() * 60000;
    const diffMinutes = (tzUtc - localUtc) / 60000;
    const offset = diffMinutes / 60;
    return offset;
  } catch {
    return 0;
  }
}

/**
 * Returns the offset difference (in hours and formatted string) between two timezones.
 * If a timeZones list is provided (with offsetMinutes), it uses that for accuracy; otherwise, falls back to dynamic calculation.
 */
export function getTimeZoneOffsetDiff(
  fromTz: string,
  toTz: string,
  timeZones?: { id: string; offsetMinutes: number }[]
): { diffHours: number; formatted: string } {
  let fromOffset: number;
  let toOffset: number;

  if (Array.isArray(timeZones) && timeZones.length > 0) {
    const from = timeZones.find((tz) => tz.id === fromTz);
    const to = timeZones.find((tz) => tz.id === toTz);
    fromOffset = from?.offsetMinutes ?? getTimeZoneOffset(fromTz) * 60;
    toOffset = to?.offsetMinutes ?? getTimeZoneOffset(toTz) * 60;
    // Convert to hours
    fromOffset = fromOffset / 60;
    toOffset = toOffset / 60;
  } else {
    fromOffset = getTimeZoneOffset(fromTz);
    toOffset = getTimeZoneOffset(toTz);
  }

  const diffHours = toOffset - fromOffset;
  let formatted: string;
  if (isNaN(diffHours)) {
    formatted = "N/A";
  } else if (diffHours === 0) {
    formatted = "0 hour";
  } else {
    formatted = `${diffHours > 0 ? "+" : ""}${diffHours} hour${Math.abs(diffHours) !== 1 ? "s" : ""}`;
  }
  return { diffHours, formatted };
}

export const getDeviceCurrentZone = () => Intl.DateTimeFormat().resolvedOptions().timeZone;


/**
 * @deprecated Use getTimeZoneOffsetDiff instead for more options and formatted output.
 */
export function getTimeZoneDiff(fromTz: string, toTz: string): number {
  const fromOffset = getTimeZoneOffset(fromTz);
  const toOffset = getTimeZoneOffset(toTz);
  return toOffset - fromOffset;
}
