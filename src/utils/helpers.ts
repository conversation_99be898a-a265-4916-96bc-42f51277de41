import { IS_ANDROID } from "@/theme/_config";
import { Nutrient } from "@/types/schemas/dietTracker";
import { PermissionsAndroid } from "react-native";
import { Asset } from "react-native-image-picker";
import notifee from "@notifee/react-native";
import dayjs from "dayjs";
import { Image } from "react-native-compressor";

export function uploadFileToAzure(
  file: Asset,
  preSignedUrl: string
): Promise<void> {
  return new Promise((resolve, reject) => {
    // Validate file type
    const allowedMimeTypes = [
      "image/jpeg",
      "image/png",
      "image/webp",
      "image/heic",
      "image/heif",
      "image/jpg",
    ];
    if (!allowedMimeTypes.includes(file.type || "")) {
      return reject(
        new Error("Invalid file type. Only image files are allowed.")
      );
    }

    // Validate file size (5 MB = 5 * 1024 * 1024 bytes)
    const maxSizeInBytes = 5 * 1024 * 1024;
    if (Number(file.fileSize) > maxSizeInBytes) {
      return reject(new Error("File size exceeds the 5MB limit."));
    }

    // Proceed with file upload
    const xhr = new XMLHttpRequest();
    xhr.open("PUT", preSignedUrl);
    xhr.setRequestHeader("x-ms-blob-type", "BlockBlob");
    xhr.setRequestHeader("Content-Type", JSON.stringify(file.type));

    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          console.error("Upload failed", xhr.status, xhr.responseText);
          reject(new Error("Failed to upload file."));
        }
      }
    };

    xhr.onerror = function () {
      console.error("Network error during file upload");
      reject(new Error("Network error during file upload."));
    };

    xhr.send(file);
  });
}

export const formatDate = (dateStr: string): string => {
  const [year, month, day] = dateStr.split("T")[0].split("-");
  return `${month}-${day}-${year}`;
};

export const validatePhoneNumber = (phone: string): string => {
  // Regex to allow common phone number formats including international numbers
  const phoneRegex =
    /^(?:\+?\d{1,3}[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;

  if (!phoneRegex.test(phone) && phone.length > 0) {
    return "Invalid phone number format. Please enter a valid phone number.";
  }

  return "";
};

export const validateEmail = (email: string): string => {
  if (!email || email.trim() === "") {
    return "Email is required.";
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return "Email is invalid.";
  }

  return "";
};

export const validateName = (name: string): string => {
  if (name.trim().length < 3) {
    return "Username must be at least 3 characters.";
  }
  return "";
};

export const validateNonZeroNumericInput = (input: string): boolean => {
  const validInput = /^(?:[1-9]\d*|0)?(\.\d{0,2})?$/; // Updated Regex
  return validInput.test(input);
};

export const getCurrentDayIndex = () => {
  const today = new Date();
  const dayIndex = today.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
  return dayIndex;
};

export const getFormattedCurrentDate = (
  date: Date | string | undefined
): string => {
  if (!date) {
    date = new Date(); // Default to current date if undefined
  }

  // Convert string to Date object if necessary
  if (typeof date === "string") {
    date = new Date(date);
  }

  // Ensure the input is a valid Date object
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error("Invalid date provided to getFormattedCurrentDate");
  }

  // Get day, month, and weekday names
  const weekday = date.toLocaleString("en-US", { weekday: "long" });
  const month = date.toLocaleString("en-US", { month: "long" });
  const day = date.getDate();

  // Add ordinal suffix
  const suffix =
    day % 10 === 1 && day !== 11
      ? "st"
      : day % 10 === 2 && day !== 12
        ? "nd"
        : day % 10 === 3 && day !== 13
          ? "rd"
          : "th";

  // Construct formatted date
  return `${weekday} ${month}, ${day}${suffix}`;
};

export const truncateText = (text: string, maxLength: number) => {
  if (!text || !maxLength) return "";
  return text.length > maxLength ? `${text.slice(0, maxLength)}..` : text;
}

export function updateDateWithTimeSlot(
  selectedDate: string,
  selectedTimeSlot: "morning" | "afternoon" | "evening" = "morning"
): Date | null {
  try {
    // Validate selectedDate
    const date = new Date(selectedDate);
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date string provided.");
    }

    // Get the current time components
    const now = new Date();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    const currentMilliseconds = now.getMilliseconds();

    // Define the hour mapping for each time slot
    const hourMapping: Record<"morning" | "afternoon" | "evening", number> = {
      morning: 0,
      afternoon: 12,
      evening: 18,
    };

    // Update the date object
    date.setUTCHours(hourMapping[selectedTimeSlot]); // Set the hour based on time slot
    date.setUTCMinutes(currentMinutes); // Preserve current minutes
    date.setUTCSeconds(currentSeconds); // Preserve current seconds
    date.setUTCMilliseconds(currentMilliseconds); // Preserve current milliseconds

    return date; // Return the updated Date object
  } catch (error) {
    console.error("Error in updateDateWithTimeSlot:", (error as Error).message);
    return null; // Return null to signify failure
  }
}

export const formatDateMMDDYYYY = (date: Date | string): string => {
  try {
    // Parse the date and use the local time
    const localDate = dayjs(date).local();

    const month = localDate.month() + 1; // Months are zero-based
    const day = localDate.date();
    const year = localDate.year();

    // Return formatted string in MM-DD-YYYY
    return `${month}-${day}-${year}`;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
};

export function findPheAndProtein(
  nutrients: Nutrient[],
  specificNutrient?: string
): Nutrient | Nutrient[] {
  const pheKey = /^phe$/i; // Regex to match "phe" or "Phe"
  const proteinKey = /^protein$/i; // Regex to match "protein" or "Protein"

  let phe: Nutrient | undefined;
  let protein: Nutrient | undefined;

  try {
    // Find the phe and protein nutrients using regex matching
    phe = nutrients.find((nutrient) => pheKey.test(nutrient.name));
    protein = nutrients.find((nutrient) => proteinKey.test(nutrient.name));

    // Calculate phe from protein if phe is not found
    if (!phe) {
      if (protein) {
        phe = {
          name: "phe",
          amount: protein.amount * 50, // Calculate phe using the formula
          unit: "mg",
        };
      } else {
        phe = {
          name: "phe",
          amount: 0,
          unit: "mg",
        };
      }
    }

    if (!protein) {
      protein = {
        name: "protein",
        amount: 0,
        unit: "g",
      };
    }

    // If a specific nutrient is requested, return only that nutrient
    if (specificNutrient) {
      if (specificNutrient.toLowerCase() === "phe") {
        return phe;
      } else if (specificNutrient.toLowerCase() === "protein") {
        return protein;
      } else {
        throw new Error(`Invalid specific nutrient: ${specificNutrient}`);
      }
    }

    return [phe, protein];
  } catch (error) {
    console.error("Error while finding nutrients:", error);
    // Return a fallback in case of error
    if (specificNutrient) {
      return {
        name: specificNutrient.toLowerCase(),
        amount: 0,
        unit: specificNutrient.toLowerCase() === "phe" ? "mg" : "g",
      };
    }
    return [
      { name: "phe", amount: 0, unit: "mg" },
      { name: "protein", amount: 0, unit: "g" },
    ];
  }
}
export const convertPheLevel = (pheLevel: string, metric: number): string => {
  const pheValue = parseFloat(pheLevel);

  if (isNaN(pheValue)) return pheLevel;

  if (metric === 1) {
    // Metric is micromoles per liter (μmol/L), no conversion needed
    return pheValue.toFixed(2);
  } else if (metric === 2) {
    // Metric is milligrams per deciliter (mg/dL), apply conversion
    const convertedValue = (pheValue * 165.19) / 10;
    return convertedValue.toFixed(2);
  }

  return pheLevel; // Default
};

interface FoodItem {
  _id: string;
  quantity: number;
  unit: string;
  nutrients: Nutrient[];
}

export interface Item {
  food_id: string;
  [key: string]: any;
}

interface MealEntry {
  fooditems: FoodItem[];
  items: Item[];
}

export function updateMealEntryProperties(mealEntry: any): any {
  const updatedMealEntry = JSON.parse(JSON.stringify(mealEntry)); // Deep clone

  updatedMealEntry.fooditems.forEach((foodItem: any, index: number) => {
    const correspondingItem = updatedMealEntry.items[index];

    // Direct property updates
    ["quantity", "unit"].forEach((prop) => {
      const matchedProp = Object.keys(correspondingItem).find(
        (key) => key.toLowerCase() === prop.toLowerCase()
      );
      if (matchedProp) {
        foodItem[prop] = correspondingItem[matchedProp];
      }
    });

    // Nutrient updates
    ["phe", "protein"].forEach((nutrientName) => {
      const matchedNutrient = foodItem.nutrients.find(
        (n: any) => n.name.toLowerCase() === nutrientName.toLowerCase()
      );

      const matchedItemProp = Object.keys(correspondingItem).find(
        (key) => key.toLowerCase() === nutrientName.toLowerCase()
      );

      if (matchedNutrient && matchedItemProp) {
        matchedNutrient.amount = correspondingItem[matchedItemProp];
      }
    });
  });

  return updatedMealEntry;
}

// A mapping of icon IDs (1 to 10) to their corresponding image assets.
// This is a static object used to allow React Native's bundler to properly include the assets.
// The object keys (1 to 10) represent the icon IDs, and the values are the required image paths.
const pillIcons: { [key: number]: any } = {
  1: require("@/theme/assets/images/pills/pill1/pill1.png"),
  2: require("@/theme/assets/images/pills/pill2/pill2.png"),
  3: require("@/theme/assets/images/pills/pill3/pill3.png"),
  4: require("@/theme/assets/images/pills/pill4/pill4.png"),
  5: require("@/theme/assets/images/pills/pill5/pill5.png"),
  6: require("@/theme/assets/images/pills/pill6/pill6.png"),
  7: require("@/theme/assets/images/pills/pill7/pill7.png"),
  8: require("@/theme/assets/images/pills/pill8/pill8.png"),
  9: require("@/theme/assets/images/pills/pill9/pill9.png"),
  10: require("@/theme/assets/images/pills/pill10/pill10.png"),
};

export const getPillIcon = (iconId: number) => {
  if (pillIcons[iconId]) {
    return pillIcons[iconId];
  }
  console.warn(`Invalid iconId: ${iconId}`);
  return null; // Or provide a default image if needed
};

export const getUnitAbbreviation = (unit: string) => {
  const unitMap: Record<string, string> = {
    Tablespoons: "tbsp",
    Teaspoons: "tsp",
  };
  return unitMap[unit] || unit; // Return abbreviation if found, else return original unit
};

export const requestPermissionForNotification = async () => {
  if (IS_ANDROID) {
    return PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  } else {
    return notifee.requestPermission();
  }
};

/**
 * Compress an image before uploading (Optimized for higher compression)
 * @param {string} uri - The original image URI
 * @param {number} maxWidth - Maximum width for the compressed image (default: 800)
 * @param {number} maxHeight - Maximum height for the compressed image (default: 800)
 * @param {number} quality - Compression quality (0 to 1, default: 0.6 for higher compression)
 * @returns {Promise<string>} - Compressed image URI
 */
export const compressImage = async (
  uri: string,
  quality = 0.6, // Reduced from 0.8 to 0.6 for better size reduction
  maxWidth = 1024, // Reduced from 1000 to 800 for more compression
  maxHeight = 1024 // Ensuring aspect ratio balance
): Promise<string> => {
  try {
    if (!uri) throw new Error("No image URI provided");

    const compressedUri = await Image.compress(uri, {
      compressionMethod: "manual", // You can switch to "auto" if needed
      maxWidth,
      maxHeight,
      quality,
    });

    return compressedUri;
  } catch (error) {
    console.error("❌ Image compression failed:", error);
    return uri; // Return original URI if compression fails
  }
};

export const removeTrailingZeros = (value: string | number): string => {
  if (typeof value === "number") {
    value = value.toString(); // Convert number to string
  }
  return parseFloat(value).toString(); // Removes trailing zeros
};

export const roundNumber = (value: string | number): number => {
  return Math.round(Number(value) || 0);
};

export const renderMedicationDosage = (
  array: any[],
  categories: any[],
  isFormula?: boolean,
  isTaskManager?: boolean,
  formulaUnitId?: number,
  formulaUnitList?: any[]
) => {
  let result = "";

  if (array.length === 0) return result;

  array.forEach((medication, index) => {
    const { quantity, strength, categoryTypeId } = medication;
    const categoryTypeName = categories.find(
      (category) => categoryTypeId === category.medicationTypeId
    )?.name;

    const dosage = isFormula ? quantity : `${quantity} x ${strength}`;

    // Get the unit text from formulaUnitList if available, otherwise use default
    let unitText = isFormula ? "g" : "mg";
    if (
      isFormula &&
      formulaUnitId &&
      formulaUnitList &&
      Array.isArray(formulaUnitList) &&
      formulaUnitList.length > 0
    ) {
      const foundUnit = formulaUnitList.find(
        (unit: any) => unit.id === formulaUnitId
      );
      if (foundUnit) {
        // Use text, label, or value property based on what's available in the formulaUnitList
        unitText = foundUnit.text || foundUnit.label || foundUnit.value || "g";
      }
    }

    result += `${dosage} ${unitText} ${categoryTypeName ?? ""}`;

    if (index !== array.length - 1) {
      result += isTaskManager ? " | " : "\n";
    }
  });

  return result;
};


export const getPheAllowanceUnit = (consumptionType: 'Phe' | 'Protein') => {
  return consumptionType === 'Phe' ? 'mg' : 'g';
}

export const getPheAllowanceValue = (allowance: any, consumptionType: 'Phe' | 'Protein') => {
  return consumptionType === 'Phe' ? allowance : allowance / 50;
}

export const setPheAllowanceValue = (allowance: any, consumptionType: 'Phe' | 'Protein') => {
  return consumptionType === 'Phe' ? allowance : allowance * 50;
}

export const getAllowanceLabel = (consumptionType: 'Phe' | 'Protein') => {
  return consumptionType === 'Phe' ? 'pheAllowance' : 'proAllowance';
}

export const getAllowancePlaceholder = (consumptionType: 'Phe' | 'Protein') => {
  return consumptionType === 'Phe' ? 'enterPheAllowance' : 'enterProAllowance';
}

export const getConvertedAllowance = (consumptionType: 'Phe' | 'Protein', allowance?: any) => {
  return {
    unit: getPheAllowanceUnit(consumptionType),
    value: getPheAllowanceValue(allowance, consumptionType),
    placeholder: getAllowancePlaceholder(consumptionType),
    label: getAllowanceLabel(consumptionType)
  }
}
