import { config, SCREEN_HEIGHT, SCREEN_WIDTH } from "@/theme/_config";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.3)'
  },
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },
  loaderContainer: {
    alignItems: "center",
    justifyContent: "center",
    zIndex: 11,
  },
  icon: {
    width: ms(50),
    height: ms(50),
    marginBottom: ms(10),
  },
  text: {
    fontSize: ms(16),
    textAlign: "center",
    color: '#fff',
    marginTop: ms(8)
  },
  scanMealText: {
    textAlign: "center",
    fontSize: ms(14),
    color: config.colors.white,
    marginTop: ms(8)
  },
  srcFoodImage: {
    width: (SCREEN_HEIGHT * 0.93) * 1.333,
    height: (SCREEN_HEIGHT * 0.93),
    position: 'absolute',
    top: -(SCREEN_HEIGHT * 0.0557),
    left: -((SCREEN_HEIGHT * 0.93) * 0.426),
  },
  lottieView: {
    width: SCREEN_WIDTH * 0.38,
    height: SCREEN_WIDTH * 0.38,
  },
  scanMealloaderContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.38)',
    justifyContent: "center",
    alignItems: "center",
    width: SCREEN_WIDTH,
    zIndex: 11,
    flex: 1,
  },
});

export default styles;
