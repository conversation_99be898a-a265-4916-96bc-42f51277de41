/** @format */

// styles/DropdownStyles.ts
import { ms, ScaledSheet } from "react-native-size-matters";

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";

const getDropdownStyles = (theme: Theme) =>
  ScaledSheet.create({
    dropdownContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: ms(10),
      height: ms(44),
      backgroundColor: theme.colors.dropDownGray,
      minWidth: ms(115),
    },
    dropdownOptions: {
      position: "absolute",
      right: 0,
      top: ms(40),
      backgroundColor: theme.colors.dropDownGray,
      width: ms(150),
      maxHeight: ms(110),
      zIndex: 10000,
    },
    dropdownOption: {
      paddingVertical: ms(8),
      paddingHorizontal: ms(10),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.lightMediumGray,
    },
    dropdownOptionText: {
      marginRight: ms(5),
      lineHeight: ms(15),
    },
    staticUnitText: {
      color: theme.colors.yellow,
      marginHorizontal: ms(10),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    modalBackground: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.0)", // Semi-transparent background
      justifyContent: "flex-start",
    },
    invertIcon: {
      transform: [{ scaleY: -1 }],
    },
  });

export default getDropdownStyles;
