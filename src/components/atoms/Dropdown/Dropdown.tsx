/** @format */

import React, { useState, useRef, useEffect } from "react";
import {
  TouchableOpacity,
  View,
  StyleProp,
  ViewStyle,
  TextStyle,
  ScrollView,
  Dimensions,
  Modal,
  LayoutChangeEvent,
  Keyboard,
  TouchableWithoutFeedback,
  AppState,
  AppStateStatus,
  Platform,
} from "react-native";
import Typography from "../Typography/Typography";
import Icons from "@/theme/assets/images/svgs/icons";
import { ms } from "react-native-size-matters";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getDropdownStyles from "./Dropdown.styles";
import { useTheme } from "@/theme";
import { DateRangeLabel } from "@/types/schemas/labs";
import CustomTextInput from "../CustomTextInput/CustomTextInput";

interface DropdownProps<T> {
  isVisible: boolean;
  data: T[];
  selectedValue: string;
  setSelectedValue: (selected: string) => void;
  onToggle: () => void;
  dropdownStyle?: StyleProp<ViewStyle>;
  buttonStyle?: StyleProp<ViewStyle>;
  optionStyle?: StyleProp<ViewStyle>;
  dropdownOptionTextStyle?: StyleProp<TextStyle>;
  placeHolder?: string;
  chevronColor?: string;
  fullHeight?: boolean;
  disabled?: boolean;
  optionTextStyle?: StyleProp<TextStyle>;
  buttonContent?: React.ReactNode;
  searchable?: boolean;
  searchPlaceholder?: string;
  searchInputStyle?: StyleProp<TextStyle>;
  isCurrent?: boolean;
  isTimezone?: boolean;
}

const { height: screenHeight } = Dimensions.get("window");

const Dropdown = <T extends { id: string | number; text: string }>({
  isVisible,
  data,
  selectedValue: selectedData,
  setSelectedValue,
  onToggle,
  dropdownStyle,
  buttonStyle,
  optionStyle,
  placeHolder = "Select an option",
  fullHeight,
  dropdownOptionTextStyle,
  disabled,
  isCurrent,
  chevronColor,
  optionTextStyle,
  buttonContent,
  searchable,
  isTimezone,
  searchInputStyle,
  searchPlaceholder,
}: DropdownProps<T>) => {
  const dropdownRef = useRef<TouchableOpacity>(null);
  const [dropdownPosition, setDropdownPosition] = useState({
    x: -10,
    y: 0,
    width: 0,
    dropdownHeight: ms(150),
  });

  const { colors } = useTheme();
  const appStateRef = useRef(AppState.currentState);
  const styles: any = useDynamicStyles(getDropdownStyles);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [searchQuery, setSearchQuery] = useState<string>("");

  useEffect(() => {
    // Handle app state changes
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if ((nextAppState === "background" || nextAppState === "inactive") && isVisible) {
        onToggle();
      }

      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [isVisible, onToggle]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener("keyboardDidShow", (event) => {
      setKeyboardHeight(event.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardHeight(0);
    });
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const updateDropdownPosition = (px: number, py: number, width: number, height: number) => {
    const safeY = py + height;
    const maxDropdownHeight = screenHeight - safeY - keyboardHeight - 20;

    setDropdownPosition({
      x: px,
      y: safeY,
      width,
      dropdownHeight: Math.max(ms(150), maxDropdownHeight),
    });
  };

  const checkAndMeasureDropdown = () => {
    dropdownRef.current?.measure((fx, fy, width, height, px, py) => {
      updateDropdownPosition(px, py, width, height);
    });
  };

  useEffect(() => {
    if (isVisible) {
      setTimeout(() => {
        checkAndMeasureDropdown();
      }, 10);
    } else {
      setSearchQuery("");
    }
  }, [isVisible, keyboardHeight]);

  const handleLayout = (event: LayoutChangeEvent) => {
    if (isVisible) {
      checkAndMeasureDropdown();
    }
  };

  // const filteredData =
  //   selectedData === DateRangeLabel.Custom ? data : data.filter((item) => item.text !== selectedData);
  const filteredData =
    selectedData === DateRangeLabel.Custom
      ? data.filter((item) => item.text.toLowerCase().includes(searchQuery.toLowerCase()))
      : data.filter(
          (item) => item.text.toLowerCase().includes(searchQuery.toLowerCase()) && item.text !== selectedData
        );
  return (
    <>
      {/* Native Modal Dropdown */}

      {Platform.OS == "android" && isTimezone ? (
        <View style={{ position: "relative" }}>
          {/* Dropdown Button */}
          {isVisible && (
            <TouchableOpacity
              activeOpacity={1}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "transparent",
                zIndex: 998,
              }}
              onPress={() => {
                Keyboard.dismiss();
                onToggle(); // Close the dropdown
              }}
            />
          )}
          <TouchableOpacity
            ref={dropdownRef}
            style={[styles.dropdownContainer, buttonStyle]}
            onPress={onToggle}
            disabled={disabled}
          >
            {buttonContent ? (
              buttonContent
            ) : (
              <Typography.B2
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.dropdownOptionText, dropdownOptionTextStyle]}
              >
                {selectedData || placeHolder}
              </Typography.B2>
            )}
            {!isCurrent && data.length > 1 && (
              <Icons.Chevron
                color={chevronColor || colors.textPrimary}
                height={12}
                width={12}
                style={isVisible ? styles.invertIcon : {}}
              />
            )}
          </TouchableOpacity>

          {/* Inline Dropdown Options */}
          {isVisible && (
            <View
              pointerEvents="auto"
              style={[
                styles.dropdownOptions,
                dropdownStyle,
                {
                  position: "absolute",
                  top: "100%",
                  left: 0,
                  right: 0,
                  width: "100%",
                  zIndex: 999,
                  backgroundColor: colors.item_secondary_bg,
                  paddingHorizontal: ms(4),
                  maxHeight: fullHeight ? "100%" : searchable ? ms(200) : ms(95),
                  overflow: "hidden",
                },
              ]}
            >
              {/* Searchable Input */}
              {searchable && (
                <View style={{ padding: ms(8) }}>
                  <CustomTextInput
                    placeholder="Search for timezones..."
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    icon={<Icons.Search />}
                  />
                </View>
              )}

              {/* Options List */}
              <ScrollView persistentScrollbar showsVerticalScrollIndicator indicatorStyle="white">
                {filteredData.map((item, index) => (
                  <TouchableOpacity
                    key={`${item.id}-${index}`}
                    style={[
                      styles.dropdownOption,
                      index !== filteredData.length - 1 && {
                        borderBottomColor: colors.white,
                        borderBottomWidth: 0.5,
                      },
                      optionStyle,
                    ]}
                    onPress={() => {
                      setSelectedValue(item.text);
                      onToggle();
                    }}
                  >
                    <Typography.B2 style={[styles.dropdownOptionText, optionTextStyle]}>{item.text}</Typography.B2>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      ) : (
        <View onLayout={handleLayout}>
          {/* Dropdown Button */}

          <TouchableOpacity
            ref={dropdownRef}
            style={[styles.dropdownContainer, buttonStyle]}
            onPress={() => {
              checkAndMeasureDropdown();
              onToggle();
            }}
            disabled={disabled}
          >
            {buttonContent ? (
              buttonContent
            ) : (
              <Typography.B2
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.dropdownOptionText, dropdownOptionTextStyle]}
              >
                {selectedData || placeHolder}
              </Typography.B2>
            )}
            {!isCurrent && data.length > 1 && (
              <Icons.Chevron
                color={chevronColor || colors.textPrimary}
                height={12}
                width={12}
                style={isVisible ? styles.invertIcon : {}}
              />
            )}
          </TouchableOpacity>
          <Modal visible={isVisible} transparent animationType="none" onRequestClose={onToggle}>
            {/* Dismiss dropdown when tapping outside */}
            <TouchableWithoutFeedback onPress={onToggle}>
              <View style={{ flex: 1 }} />
            </TouchableWithoutFeedback>

            {/* Dropdown Options */}
            <View
              style={[
                styles.dropdownOptions,
                dropdownStyle,
                {
                  position: "absolute",
                  top: dropdownPosition.y,
                  left: dropdownPosition.x,
                  width: dropdownPosition.width,
                  maxHeight: fullHeight ? "100%" : searchable ? ms(180) : ms(95), // Adjust
                  overflow: "hidden",
                  paddingHorizontal: ms(4),
                },
              ]}
            >
              {/* Searchable Input for dropdown search */}
              {searchable && (
                <View style={{ padding: ms(8) }}>
                  <CustomTextInput
                    placeholder="Search for timezones..."
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    icon={<Icons.Search />}
                  />
                </View>
              )}
              <ScrollView
                style={{ maxHeight: dropdownPosition.dropdownHeight }}
                persistentScrollbar={true}
                showsVerticalScrollIndicator={true}
                indicatorStyle="white"
              >
                {filteredData.map((item, index) => (
                  <TouchableOpacity
                    key={`${item.id}-${index}`}
                    style={[
                      styles.dropdownOption,
                      index !== filteredData.length - 1 && {
                        borderBottomColor: colors.white,
                        borderBottomWidth: 0.5,
                      },
                      optionStyle,
                    ]}
                    onPress={() => {
                      setSelectedValue(item.text);
                      onToggle();
                    }}
                  >
                    <Typography.B2 style={[styles.dropdownOptionText, optionTextStyle]}>{item.text}</Typography.B2>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </Modal>
        </View>
      )}
    </>
  );
};

export default Dropdown;
