import { config, SCREEN_HEIGHT } from "@/theme/_config";
import { ScaledSheet } from "react-native-size-matters";

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: "black",
  },
  camera: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  flashButton: {
    position: "absolute",
    top: "20@s",
    left: "20@s",
    zIndex: 1,
    borderRadius: "35@s",
    backgroundColor: '#3535354D',
    padding: "10@s",
  },
  closeButton: {
    position: "absolute",
    top: "20@s",
    right: "20@s",
    backgroundColor: '#3535354D',
    borderRadius: "35@s",
    zIndex: 1,
  },
  carousel: {
    position: "absolute",
    top: "56@ms",
    width: "100%",
    zIndex: 1,
  },
  cameraFrame: {
    position: "absolute",
    top: SCREEN_HEIGHT*0.265,
    width: "100%",
    alignSelf:'center',
    zIndex: 1,
  },
  bottomControls: {
    position: "absolute",
    bottom: "30@s",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: "30@s",
  },
  mediaPicker: {
    width: "50@s",
    height: "50@s",
    borderRadius: "10@s",
    overflow: "hidden",
    backgroundColor: "gray",
  },
  shutterButton: {
    width: "70@s",
    height: "70@s",
    borderRadius: "35@s",
    borderWidth: "4@s",
    borderColor: "white",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
  },
  shutterInner: {
    width: "50@s",
    height: "50@s",
    borderRadius: "25@s",
    backgroundColor: "white",
  },
  cameraToggle: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: "35@s",
    backgroundColor: '#3535354D',
  },
  center: {
    justifyContent: "center",
    alignItems: "center",
  },
  mediaPreview: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
});

export default styles;
