import { KeyboardAvoidingView, Platform, StatusBar, ViewStyle } from "react-native";
import type { PropsWithChildren } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useTheme } from "@/theme";

interface SafeScreenProps extends PropsWithChildren {
  containerStyle?: ViewStyle;
}

function SafeScreen({ children, containerStyle}: SafeScreenProps  ) {
  const insets = useSafeAreaInsets();
  const { variant, navigationTheme } = useTheme();

  return (
    <KeyboardAvoidingView
      style={[
      {  flex: 1,
        backgroundColor: navigationTheme.colors.background,
        paddingTop: insets.top,
        paddingLeft: insets.left,
        paddingRight: insets.right,
        paddingBottom: insets.bottom,}, 
        containerStyle
      ]}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <StatusBar
        backgroundColor={navigationTheme.colors.background}
        barStyle={variant === "dark" ? "light-content" : "dark-content"}
      />
      {children}
    </KeyboardAvoidingView>
  );
}

export default SafeScreen;
