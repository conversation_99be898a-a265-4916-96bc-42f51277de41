import React from "react";
import { View } from "react-native";

import { useTheme } from "@/theme";
import Common from "@/theme/common.style";
import { SCREEN_WIDTH } from "@/theme/_config";
import Icons from "@/theme/assets/images/svgs/icons";
import { getTimeZoneLabel } from "@/utils/timezoneUtils";
import { Typography, Button } from "@/components/atoms";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import getGlobalTimeZoneModal from "./GlobalTimeZoneModal.styles";

interface GlobalTimeZoneModalProps {
  isVisible: boolean;
  currentTimeZone: string;
  profileTimeZone: string;
  timeZoneDifference: string;
  onKeepCurrent: () => void;
  onUpdateNew: () => void;
  onClose?: () => void;
  modalWidth?: number | string; // Optional: allow custom width
  modalHeight?: number | string; // Optional: allow custom height
  timeZones: { id: string; displayName: string }[]; // <-- add this
}

const GlobalTimeZoneModal: React.FC<GlobalTimeZoneModalProps> = ({
  isVisible,
  currentTimeZone,
  profileTimeZone,
  timeZoneDifference,
  onKeepCurrent,
  onUpdateNew,
  onClose,
  modalWidth = SCREEN_WIDTH * 0.9, // default width
  modalHeight, // default height (auto)
  timeZones,
}) => {
  const theme = useTheme();
  const styles = getGlobalTimeZoneModal(theme);

  return (
    <GenericModal
      isVisible={isVisible}
      onClose={onClose}
      modalStyle={{ width: modalWidth, height: modalHeight }} // Pass to GenericModal
      customHeader={
        <View style={styles.headerContainer}>
          <Icons.Clock width={48} height={48} color={theme.colors.textPrimary}/>
          <Typography.H2 style={styles.headerTitle}>
            You're in a new{"\n"}time zone.
          </Typography.H2>
        </View>
      }
      customBody={
        <View>
          <Typography.B1 style={styles.bodyText}>
            Do you want to update your routine to match the new time zone, or
            keep it at the original time?
          </Typography.B1>
          <Typography.B3 style={styles.bodyExample}>
            For example, if your routine was set for 8 AM and you moved from New
            York to London, do you want it to now run at 8 AM London time or
            still run at what would be 8 AM New York time (1 PM London time)?
          </Typography.B3>
          <View style={styles.timeZoneInfoContainer}>
            <Typography.B2 style={styles.labelMuted}>
              Current Time Zone
            </Typography.B2>
            <Typography.H3 style={styles.labelHighlight}>
              {getTimeZoneLabel(currentTimeZone, timeZones)}
            </Typography.H3>
            <Typography.B2 style={styles.labelMuted}>
              Your profile Time Zone
            </Typography.B2>
            <Typography.H3 style={styles.labelHighlight}>
              {getTimeZoneLabel(profileTimeZone, timeZones)}
            </Typography.H3>
            <Typography.B2>
              Difference:{" "}
              <Typography.B2 style={styles.labelHighlight}>
                {timeZoneDifference}
              </Typography.B2>
            </Typography.B2>
          </View>
        </View>
      }
      customFooter={
        <View>
          <Button.Outline onPress={onKeepCurrent} style={styles.buttonOutline}>
            <Typography.B2 style={[styles.buttonText, Common.textBold]}>
              Keep current time zone
            </Typography.B2>
          </Button.Outline>
          <Button.Main onPress={onUpdateNew}>
            <Typography.B2 style={[styles.buttonText, styles.buttonTextPrimary, Common.textBold]}>
              Update new time zone
            </Typography.B2>
          </Button.Main>
        </View>
      }
    />
  );
};

export default GlobalTimeZoneModal;
