import React, { useEffect, useRef } from "react";
import { ViewStyle, TextStyle, Pressable, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { useTheme } from "@/theme";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getAnimatedSelectorStyle from "./AnimatedSelectors.styles";
import { Typography } from "@/components/atoms";
import { ms } from "react-native-size-matters";
import { useAnimatedBorderScale } from "@/hooks/onboarding/useRoleSelectAnimations";
export interface IRoleOptions {
  id: number;
  label?: string;
  icon?: () => React.ReactNode;
  backgroundColor?: string;
}

interface RoleSelectorProps {
  item: IRoleOptions;
  isSelected: boolean;
  onToggle: (item: IRoleOptions) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
}

const RoleSelector: React.FC<RoleSelectorProps> = ({
  item,
  isSelected,
  onToggle,
  containerStyle,
  labelStyle,
}) => {
  const styles: any = useDynamicStyles(getAnimatedSelectorStyle);
  const { colors } = useTheme();

  const iconSize = useSharedValue(90);
  const iconMarginTop = useSharedValue(ms(-15));
  const cardHeight = useSharedValue(80);
  const containerMarginTop = useSharedValue(ms(25));
  const hasMounted = useRef(false);

  useEffect(() => {
    if (!hasMounted.current) {
      iconSize.value = isSelected ? ms(140) : ms(85);
      iconMarginTop.value = isSelected ? ms(-45) : ms(-15);
      cardHeight.value = isSelected ? ms(100) : ms(70);
      containerMarginTop.value = isSelected ? ms(55) : ms(25);
      hasMounted.current = true;
      return;
    }

    iconSize.value = withTiming(isSelected ? ms(140) : ms(85), {
      duration: 400,
    });
    iconMarginTop.value = withTiming(isSelected ? ms(-45) : ms(-15), {
      duration: 400,
    });
    cardHeight.value = withTiming(isSelected ? ms(100) : ms(70), {
      duration: 400,
    });
    containerMarginTop.value = withTiming(isSelected ? ms(55) : ms(25), {
      duration: 400,
    });
  }, [isSelected]);

  const { animatedBorderStyle } = useAnimatedBorderScale({
    isActive: isSelected,
    activeColor:
      item.id === 1
        ? colors.yellow
        : item.id === 2
          ? colors.slightLightGrey
          : colors.lightGreen,
    inactiveColor: colors.transparent,
  });

  const animatedCardStyle = useAnimatedStyle(() => {
    return {
      height: cardHeight.value,
      marginTop: containerMarginTop.value,
    };
  });

  const animatedIconStyle = useAnimatedStyle(() => ({
    width: iconSize.value,
    height: iconSize.value,
    marginTop: iconMarginTop.value,
  }));

  const handlePress = () => onToggle(item);

  const renderContent = () => {
    if (item?.id === 1 || item?.id === 3) {
      return (
        <>
          <Typography.H2
            style={[
              styles.roleLabels,
              labelStyle,
              {
                color: item.id === 3 ? colors.tile_bg : colors.white,
              },
            ]}
            numberOfLines={1}
          >
            {item?.label}
          </Typography.H2>
          <Animated.View style={animatedIconStyle}>
            {item.icon?.()}
          </Animated.View>
        </>
      );
    }
    return (
      <>
        <Animated.View style={animatedIconStyle}>{item.icon?.()}</Animated.View>
        <Typography.H2
          style={[
            styles.roleLabels,
            labelStyle,
            {
              color: item.id === 3 ? colors.tile_bg : colors.white,
            },
          ]}
          numberOfLines={1}
        >
          {item?.label}
        </Typography.H2>
      </>
    );
  };

  return (
    <Pressable onPress={handlePress}>
      <Animated.View
        style={[
          styles.roleContainer,
          animatedBorderStyle,
          animatedCardStyle,
          containerStyle,
          {
            backgroundColor: item.backgroundColor,
            borderWidth: isSelected ? ms(5) : ms(0),
          },
        ]}
      >
        <View style={styles.centerContainer}>{renderContent()}</View>
      </Animated.View>
    </Pressable>
  );
};

export default RoleSelector;
