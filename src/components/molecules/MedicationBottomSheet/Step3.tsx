import { useWatch } from "react-hook-form";
import { Control } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { ms } from "react-native-size-matters";
import { ScrollView, View } from "react-native";
import { SCREEN_HEIGHT } from "@gorhom/bottom-sheet";

import { useAppSelector } from "@/store";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import InputField from "../Field/InputField";
import { Typography } from "@/components/atoms";
import { Sections } from "@/types/schemas/task";
import RNCalendar from "@/components/atoms/RNCalendar/RNCalendar";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { getMedicationSheetStyles } from "./MedicationBottomSheet.style";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";
import InputDropdown from "@/components/atoms/InputDropdown/InputDropdown";
import RNDateTimePicker from "@/components/atoms/RNDateTimePicker/RNDateTimePicker";

const Step3 = ({
  control,
  getFrequencyName,
  customUpdateHandler,
  isFormula,
}: {
  control: Control;
  getFrequencyName: (id: number) => string;
  customUpdateHandler?: (isFormula: boolean) => void;
  isFormula: boolean;
}) => {
  const { t } = useTranslation("taskManager");
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  const { frequencies, reminderTimes, fromUpdate } = useAppSelector(selectTask);
  const customSchedule = useWatch({ control, name: "customSchedule" });
  const frequencyTypeId = useWatch({ control, name: "frequencyTypeId" });

  const isCustomValue =
    frequencyTypeId?.id === 11 &&
    customSchedule?.frequency?.id &&
    customSchedule?.interval?.id;
  return (
    <ScrollView
      contentContainerStyle={[styles.contentContainer]}
      nestedScrollEnabled
    >
      <View>
        <Typography.H1 style={styles.title}>{t("schedule")}</Typography.H1>
        <View style={{ zIndex: 99999 }}>
          <InputField
            name="frequencyTypeId"
            control={control}
            label={t("repeats")}
            component={InputDropdown}
            defaultValue={{
              id: frequencies?.[1]?.id, // NOTE: default value of the frequency will be Daily ==> expose this to the enums later
              label: frequencies?.[1]?.text,
              value: frequencies?.[1]?.id,
            }}
            trigger="onSelect"
            wrapperStyle={{ marginVertical: 0 }}
            menuHeight={SCREEN_HEIGHT / 3.5}
            menu={
              frequencies?.map((frequency) => ({
                id: frequency.id,
                label: frequency.text,
                value: frequency.id,
              })) || []
            }
            fromUpdate={(item) => {
              if (item?.id === 11 && customUpdateHandler) {
                if (!fromUpdate) {
                  customUpdateHandler(isFormula);
                }
                customUpdateHandler(fromUpdate === Sections.FORMULA);
              }
            }}
          />
        </View>

        {isCustomValue ? (
          <View
            style={{
              flexDirection: "row",
              alignItems: "flex-start",
              marginTop: ms(28),
            }}
          >
            <View style={{ marginRight: ms(30) }}>
              <Typography.H5
                style={{
                  lineHeight: 20,
                  marginBottom: 5,
                  width: ms(114),
                  color: config.backgrounds.mediumGray,
                }}
              >
                Frequency
              </Typography.H5>
              <Typography.H5 style={{ lineHeight: 20, marginBottom: 5 }}>
                {customSchedule?.frequency?.label}
              </Typography.H5>
            </View>

            <View style={{ flex: 0.85 }}>
              <Typography.H5
                style={{
                  lineHeight: 20,
                  marginBottom: 5,
                  width: ms(114),
                  color: config.backgrounds.mediumGray,
                }}
              >
                Every
              </Typography.H5>
              <Typography.H5 style={{ lineHeight: 20, marginBottom: 5 }}>
                {`${customSchedule?.interval?.label} ${getFrequencyName(customSchedule?.frequency?.label) || ""}`}
              </Typography.H5>
            </View>
          </View>
        ) : (
          <View style={{ height: ms(28) }} />
        )}

        <View
          style={{
            flexDirection: "row",
            alignItems: "flex-start",
          }}
        >
          <View
            style={{
              marginRight: ms(30),
            }}
          >
            <InputField
              name="intakeTime"
              control={control}
              component={RNDateTimePicker}
              label={t("intakeTime")}
              wrapperStyle={{ width: ms(114), height: ms(36) }}
              mode="time"
              trigger="onChange"
              pickerProps={{}}
            />
          </View>

          <View style={{ flex: 1, zIndex: 9998 }}>
            <InputField
              name="reminder"
              inputStyle={styles.reminderInput}
              control={control}
              label={t("remindMe")}
              menuHeight={ms(130)}
              itemHeight={ms(36)}
              dropdownTextStyle={{ textAlign: "center", maxWidth: "96%" }}
              component={InputDropdown}
              defaultValue={{
                id: reminderTimes?.[0]?.id, // NOTE: default value of the reminder will be At the scheduled time ==> expose this to the enums later
                label: reminderTimes?.[0]?.text,
                value: reminderTimes?.[0]?.id,
              }}
              showsVerticalScrollIndicator={true}
              trigger="onSelect"
              menu={
                reminderTimes?.map((reminderTime) => ({
                  id: reminderTime.id,
                  label: `${reminderTime.text}${reminderTime.id <= 0 ? "" : " before"}`,
                  value: reminderTime.id,
                })) || []
              }
            />
          </View>
        </View>
      </View>

      <View style={{ marginTop: ms(32), zIndex: -1 }}>
        <Typography.B1 style={Common.textBold} numberOfLines={1}>
          {t("Duration")}
        </Typography.B1>
      </View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          zIndex: -1,
        }}
      >
        <View style={{ marginRight: ms(30) }}>
          <InputField
            name="fromDate"
            control={control}
            trigger="onSelect"
            component={RNCalendar}
            label={t("From")}
            wrapperStyle={{ width: ms(114) }}
            mode="date"
          />
        </View>

        <View style={{ flex: 0.85 }}>
          <InputField
            name="toDate"
            control={control}
            trigger="onSelect"
            component={RNCalendar}
            label={t("Until")}
            mode="date"
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default Step3;
