/** @format */

import React from "react";
import { KeyboardAvoidingView, Platform, Pressable, View } from "react-native";
import { ms } from "react-native-size-matters";
import { Typography } from "@/components/atoms";

import { useTheme } from "@/theme";
import Common from "@/theme/common.style";
import Button from "@/components/atoms/Button/Button";
import Dropdown from "@/components/atoms/Dropdown/Dropdown";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import getTimeZoneBottomSheetStyles from "./TimeZoneBottomSheet.styles";
import { SCREEN_HEIGHT } from "@/theme/_config";
import { truncateText } from "@/utils/helpers";

interface TimeZoneDropdownItem {
  id: string;
  text: string;
}

interface TimeZoneBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  height?: number;
  currentTimeZone: string;
  selectedTimeZone: string;
  timeZoneDifference: string;
  timeZoneDropdownData: TimeZoneDropdownItem[];
  currentTzDropdownVisible: boolean;
  setCurrentTzDropdownVisible: (v: boolean) => void;
  newTzDropdownVisible: boolean;
  setNewTzDropdownVisible: (v: boolean) => void;
  setSelectedTimeZone: (id: string) => void;
  getTimeZoneLabel: (tz: string) => string;
  onUpdate: () => void;
  loading?: boolean;
}

const TimeZoneBottomSheet: React.FC<TimeZoneBottomSheetProps> = ({
  isVisible,
  onClose,
  height = SCREEN_HEIGHT * 0.84,
  currentTimeZone,
  selectedTimeZone,
  timeZoneDifference,
  timeZoneDropdownData,
  currentTzDropdownVisible,
  setCurrentTzDropdownVisible,
  newTzDropdownVisible,
  setNewTzDropdownVisible,
  setSelectedTimeZone,
  getTimeZoneLabel,
  onUpdate,
  loading = false,
}) => {
  const theme = useTheme(); // or get from context/provider
  const styles = getTimeZoneBottomSheetStyles(theme);
  const handlePressOutside = () => {
    if (Platform.OS == "android") {
      console.log("pressed");
      setNewTzDropdownVisible(false);
    }
  };
  return (
    // <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={{ flex: 1 }}>
    <BottomSheetWrapper
      isVisible={isVisible}
      onClose={() => {
        onClose();
        handlePressOutside();
      }}
      height={height}
    >
      <Pressable onPress={handlePressOutside} style={{ flex: 1 }}>
        <View style={styles.timeZoneSheetContainer}>
          <Typography.H2 style={styles.timeZoneSheetTitle}>Time zone</Typography.H2>
          {/* Current time zone (disabled) */}
          <View style={styles.timeZoneSection}>
            <View style={styles.timeZoneDropdownWrapper}>
              <Dropdown
                chevronColor={theme.colors.gray}
                isVisible={currentTzDropdownVisible}
                data={timeZoneDropdownData}
                selectedValue={getTimeZoneLabel(currentTimeZone)}
                setSelectedValue={() => {}}
                onToggle={() => setCurrentTzDropdownVisible(!currentTzDropdownVisible)}
                disabled
                isTimezone
                isCurrent
                buttonStyle={styles.dropdownButton}
                buttonContent={
                  <View style={{ flexDirection: "row", gap: ms(8) }}>
                    <Typography.B2 style={[styles.buttonContentLeft, Common.textBold]}>Current time zone</Typography.B2>
                    <Typography.B2 style={[styles.textPink, Common.textBold]}>
                      {truncateText(getTimeZoneLabel(currentTimeZone), 26)}
                    </Typography.B2>
                  </View>
                }
                optionStyle={styles.optionStyle}
              />
            </View>
          </View>
          {/* New time zone (selectable) */}
          <View style={styles.timeZoneSection}>
            <View style={styles.timeZoneDropdownWrapper}>
              <Dropdown
                isVisible={newTzDropdownVisible}
                data={timeZoneDropdownData}
                selectedValue={getTimeZoneLabel(selectedTimeZone)}
                setSelectedValue={(label) => {
                  const found = timeZoneDropdownData.find((item) => item.text === label);
                  if (found) setSelectedTimeZone(found.id);
                }}
                onToggle={() => setNewTzDropdownVisible(!newTzDropdownVisible)}
                dropdownStyle={styles.dropdownStyle}
                buttonStyle={[styles.dropdownButton, newTzDropdownVisible && styles.dropdownButtonOpen]}
                buttonContent={
                  <View style={{ flexDirection: "row", gap: ms(8) }}>
                    <Typography.B2 style={[styles.buttonContentLeft, Common.textBold]}>New time zone</Typography.B2>
                    <Typography.B2 lineBreakMode="middle" style={[styles.textPink, Common.textBold]}>
                      {truncateText(getTimeZoneLabel(selectedTimeZone), 26)}
                    </Typography.B2>
                  </View>
                }
                isTimezone
                searchable
                searchPlaceholder="Search time zones..."
                optionStyle={styles.optionStyle}
              />
            </View>
          </View>
          {/* Difference */}
          {!newTzDropdownVisible && (
            <>
              <Typography.B2>
                Difference: <Typography.B2 style={[styles.textPink]}>{timeZoneDifference}</Typography.B2>
              </Typography.B2>
            </>
          )}
          <View style={{ flex: 0.9 }} />
          <Button.Main style={styles.timeZoneUpdateButton} onPress={onUpdate} disabled={loading}>
            <Typography.B1 style={[Common.textBold, styles.btnText]}>
              {loading ? "Updating..." : "Update time zone"}
            </Typography.B1>
          </Button.Main>
        </View>
      </Pressable>
    </BottomSheetWrapper>

    // </KeyboardAvoidingView>
  );
};

export default TimeZoneBottomSheet;
