/** @format */

import React, { useCallback, useState } from "react";
import { View } from "react-native";
import { Button, Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getBottomStyles from "./BottomAuthSection.styles";
import Common from "@/theme/common.style";
import Icons from "@/theme/assets/images/svgs/icons";
import { useTranslation } from "react-i18next";
import { LOGIN_OPTIONS } from "@/constants/auth0";
import { AUTH_PROVIDERS, AuthProviderKey } from "@/constants/authProviders";
import { useAuth0Authentication } from "@/hooks/useAuth0Authentication";
import GenericModal from "../GenericModal/GenericModal";
import { useAuth0 } from "react-native-auth0";
import { useTheme } from "@/theme";

interface Props {
  lastUsedAuthMethod: string | null;
  onLoginPress: () => void;
  onSwitchMethod: () => void;
  onSignupPress: () => void;
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
}

const BottomAuthSection: React.FC<Props> = ({
  lastUsedAuthMethod,
  onLoginPress,
  onSwitchMethod,
  onSignupPress,
  setIsLoading,
  isLoading,
}) => {
  const styles: any = useDynamicStyles(getBottomStyles);
  const {clearCredentials} = useAuth0()
  const { t } = useTranslation(["login"]);
  const { variant } = useTheme();
  const { loginWithConnection } = useAuth0Authentication(false);
    const [modalConfig, setModalConfig] = useState<{
      visible: boolean;
      headerText: string;
      confirmText?: string;
      closeText?: string;
      onConfirm?: () => void;
    }>({
      visible: false,
      headerText: "",
      confirmText: "",
      closeText: "Close",
      onConfirm: () => {},
    });
  
  const loginWithOption = useCallback(
    (connection: string, label: string) => async () => {
      setIsLoading(true);
      try {
      const result= await loginWithConnection(connection, label);
       if (result?.noAccountCreated) {
          setModalConfig({
            visible: true,
            headerText: "You do not have an account with us.",
            confirmText: "Create Account",
            closeText: "Close",
            onConfirm: () => {
              setModalConfig((prev) => ({ ...prev, visible: false }));
              onSignupPress()
            },
          });
        }
      } catch (e) {
         setModalConfig({
            visible: true,
            headerText: e?.message ? e?.message : "Authentication failed, please try another option",
            closeText: "Close",
          });
        console.error("Login failed:", e);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );
  const renderAuthButton = () => {
    if (!lastUsedAuthMethod) return null;
    if (Object.prototype.hasOwnProperty.call(AUTH_PROVIDERS, lastUsedAuthMethod)) {
      const provider = AUTH_PROVIDERS[lastUsedAuthMethod as AuthProviderKey];
      return (
        <Button.Auth onPress={loginWithOption(provider?.connection, provider?.label)} disabled={isLoading}>
          <View style={Common.rowJustifyCenter}>
            <View style={styles.baseView}>
              {provider.icon}
              <Typography.B1 style={styles.authText}>{t("continueWith", { connection: provider.label })}</Typography.B1>
            </View>
          </View>
        </Button.Auth>
      );
    } else {
      return (
        <Button.Main style={styles.emailButton} onPress={onLoginPress}>
          <Typography.B1 style={styles.loginButtonText}>Continue</Typography.B1>
        </Button.Main>
      );
    }
     
  };

  const Divider = () => {
    return <View style={styles.divider} />;
  };

  return (
    <View style={styles.bottomSection}>
      {renderAuthButton()}
      <View style={styles.orContainer}>
        <Divider />
        <Typography.B1 style={styles.orText}>OR</Typography.B1>
        <Divider />
      </View>

      <Button.YellowOutline onPress={onSwitchMethod} style={styles.createAccountButton}>
        <Typography.B1 style={styles.createButtonText}>Use another method</Typography.B1>
      </Button.YellowOutline>

      <Typography.B3 style={styles.subText}>
        Don’t have an account?{" "}
        <Typography.B3 style={styles.signup} onPress={onSignupPress}>
          Sign up
        </Typography.B3>
      </Typography.B3>
       <GenericModal
            isVisible={modalConfig.visible}
            onConfirm={modalConfig.onConfirm}
            onClose={() => {
                clearCredentials()
                setModalConfig((prev) => ({ ...prev, visible: false }))}
                }
            headerText={modalConfig.headerText}
            confirmText={modalConfig.confirmText}
            closeText={modalConfig.closeText}
            svgSource={variant==="dark" ? <Icons.Warning width={40} height={40} /> : <Icons.WarningLight width={40} height={40}/>}
          />
    </View>
  );
};

export default BottomAuthSection;
