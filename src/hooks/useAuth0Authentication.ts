/** @format */

// src/hooks/useAuth0Login.ts
import { useAuth0 } from "react-native-auth0";
import jwtDecode from "jwt-decode";
import { useAppDispatch, useAppSelector } from "@/store";
import { login, setIdToken } from "@/store/slices/authSlice";
import { createUserThunk } from "@/store/slices/userSlice";
import axiosInstance from "@/services/axiosInstance";
import { initializeTimezoneThunk, setLastLoginMethod } from "@/store/slices/settingsSlice";
import { AUTH_METHOD, deleteAuth0User, selectAuthMethod } from "@/store/slices/onboardingSlice";
import { useCallback } from "react";

interface DecodedToken {
  email?: string;
  name?: string;
  nickname?: string;
  sub?: string;
  phone?: string;
}

export const useAuth0Authentication = () => {
  const { authorize, getCredentials, user } = useAuth0();
  const dispatch = useAppDispatch();
  const authMethod = useAppSelector(selectAuthMethod);
  const isSignup = authMethod === AUTH_METHOD.SIGN_UP;

  const loginWithConnection = useCallback(async (connection: string, label: string) => {
    if (isSignup) {
      try {
        await authorize(
          {
            connection,
            scope: "openid profile email phone",
            additionalParameters: { prompt: "login" },
          },
          { ephemeralSession: false }
        );

        const response = await getCredentials();

        if (response?.idToken) {
          axiosInstance.defaults.headers.common["Authorization"] =
            response.idToken;

          const decodedToken = jwtDecode(response.idToken) as DecodedToken;

          if (decodedToken?.logins_count > 1) {
            return {
              alreadyExists: true,
              user: user,
              decodedToken: decodedToken,
              idToken: response.idToken,
              noAccountCreated: false
            };
          }

          if (!decodedToken?.email && decodedToken?.sub?.includes("facebook")) {
            decodedToken.email = `${decodedToken.sub}@facebook.com`;
          }

          const payloadForUser = {
            email: decodedToken?.email || "",
            name: decodedToken?.name || "",
            nickname: decodedToken?.nickname || "",
            userSubId: decodedToken?.sub || "",
            phoneNumber: decodedToken?.phone || "",
          };

          dispatch(createUserThunk(payloadForUser))
            .unwrap()
            .then((res) => {
              // if (res && res.onBoarded && TimezoneService.isEmptyOrUTC(res.timeZoneId))
              if (res && res.onBoarded) {
                dispatch(initializeTimezoneThunk())
              }
            });
          dispatch(setIdToken(response.idToken));
          dispatch(login(user));
          dispatch(setLastLoginMethod(label.toLowerCase()));
        }
      } catch (error) {
        console.error("Login failed:", error);
        throw error;
      }
    } else {
      try {
        await authorize(
          {
            connection,
            scope: "openid profile email phone",
            additionalParameters: { prompt: "login" },
          },
          { ephemeralSession: false }
        );

        const response = await getCredentials();
        if (response?.idToken) {
          axiosInstance.defaults.headers.common["Authorization"] =
            response.idToken;

          const decodedToken = jwtDecode(response.idToken) as DecodedToken;

          if (!decodedToken?.email && decodedToken?.sub?.includes("facebook")) {
            decodedToken.email = `${decodedToken.sub}@facebook.com`;
          }
          if (decodedToken?.logins_count === 1) {
            try {
              await dispatch(deleteAuth0User(decodedToken?.sub)).unwrap();
              return { noAccountCreated: true };
            } catch (error) {
              console.error("Error deleting Auth0 user:", error);
              return { error };
            }
          }
          const payloadForUser = {
            email: decodedToken?.email || "",
            name: decodedToken?.name || "",
            nickname: decodedToken?.nickname || "",
            userSubId: decodedToken?.sub || "",
            phoneNumber: decodedToken?.phone || "",
          };

          dispatch(createUserThunk(payloadForUser))
            .unwrap()
            .then((res) => {
              // if (res && res.onBoarded && TimezoneService.isEmptyOrUTC(res.timeZoneId))
              if (res && res.onBoarded) {
                dispatch(initializeTimezoneThunk())
              }
            });
          dispatch(setIdToken(response.idToken));
          dispatch(login(user));
          dispatch(setLastLoginMethod(label.toLowerCase()));
        }
      } catch (error) {
        console.error("Login failed:", error);
        throw error;
      }
    }
  }, [isSignup, authMethod])

  return { loginWithConnection, isSignup, authMethod };
};
