import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { TimezoneService } from '@/utils/timezoneService';
import { selectProfileTimeZone, selectShowTimeZoneModal } from '@/store/slices/settingsSlice';

export const useTimezone = () => {
  const profileTimeZone = useAppSelector(selectProfileTimeZone);
  const showTimeZoneModal = useAppSelector(selectShowTimeZoneModal);

  const initializeTimezone = useCallback(async () => {
    await TimezoneService.initializeTimezone();
  }, []);

  const checkTimezoneOnFocus = useCallback(async () => {
    await TimezoneService.checkTimezoneOnFocus();
  }, []);

  const updateToNewTimeZone = useCallback(async (newTimeZone: string, isModal?:boolean, restrictFetchingUser?:boolean) => {
    await TimezoneService.updateToNewTimeZone(newTimeZone, isModal, restrictFetchingUser);
  }, []);

  const keepCurrentTimeZone = useCallback(() => {
    TimezoneService.keepCurrentTimeZone();
  }, []);

  const refreshUserAndTimezone = useCallback(async () => {
    await TimezoneService.refreshUserAndTimezone();
  }, []);

  const getDeviceTimeZone = useCallback(() => {
    return TimezoneService.getDeviceTimeZone();
  }, []);

  return {
    profileTimeZone,
    showTimeZoneModal,
    initializeTimezone,
    checkTimezoneOnFocus,
    updateToNewTimeZone,
    keepCurrentTimeZone,
    refreshUserAndTimezone,
    getDeviceTimeZone,
  };
}; 